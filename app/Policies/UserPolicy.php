<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;
use Modules\Core\Models\Admin;
use Modules\Institute\Concerns\UserTypes;

final class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the admin can view any users.
     */
    public function viewAny(Admin|User $user): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('view any users');
        }

        // Regular user access control
        return match ($user->type) {
            UserTypes::INSTITUTION => true,
            UserTypes::TEACHER => true,
            UserTypes::STUDENT => false,
        };
    }

    /**
     * Determine whether the admin/user can view the model.
     */
    public function view(Admin|User $user, User $model): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('view users');
        }

        // Users can always view their own profile
        if ($user->id === $model->id) {
            return true;
        }

        return match ($user->type) {
            UserTypes::INSTITUTION => $this->sameInstitute($user, $model),
            UserTypes::TEACHER => $this->canTeacherViewUser($user, $model),
            UserTypes::STUDENT => false,
        };
    }

    /**
     * Determine whether the admin/user can create users.
     */
    public function create(Admin|User $user): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('create users');
        }

        // Only institutions can create new users (students/teachers)
        return $user->type === UserTypes::INSTITUTION;
    }

    /**
     * Determine whether the admin/user can update the model.
     */
    public function update(Admin|User $user, User $model): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('update users');
        }

        // Users can always update their own profile
        if ($user->id === $model->id) {
            return true;
        }

        return match ($user->type) {
            UserTypes::INSTITUTION => $this->canInstitutionManageUser($user, $model),
            UserTypes::TEACHER => false, // Teachers cannot update other users
            UserTypes::STUDENT => false,
        };
    }

    /**
     * Determine whether the admin/user can delete the model.
     */
    public function delete(Admin|User $user, User $model): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('delete users');
        }

        // Users cannot delete themselves
        if ($user->id === $model->id) {
            return false;
        }

        return match ($user->type) {
            UserTypes::INSTITUTION => $this->canInstitutionManageUser($user, $model),
            UserTypes::TEACHER => false,
            UserTypes::STUDENT => false,
        };
    }

    /**
     * Determine whether the admin/user can restore the model.
     */
    public function restore(Admin|User $user, User $model): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('restore users');
        }

        return match ($user->type) {
            UserTypes::INSTITUTION => $this->canInstitutionManageUser($user, $model),
            UserTypes::TEACHER => false,
            UserTypes::STUDENT => false,
        };
    }

    /**
     * Determine whether the admin/user can permanently delete the model.
     */
    public function forceDelete(Admin|User $user, User $model): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('force delete users');
        }

        return match ($user->type) {
            UserTypes::INSTITUTION => $this->canInstitutionManageUser($user, $model),
            UserTypes::TEACHER => false,
            UserTypes::STUDENT => false,
        };
    }

    /**
     * Determine whether the admin/user can manage institute settings.
     */
    public function manageInstitute(Admin|User $user): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('manage institutes');
        }

        return $user->type === UserTypes::INSTITUTION;
    }

    /**
     * Determine whether the admin/user can assign roles.
     */
    public function assignRoles(Admin|User $user): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('assign user roles');
        }

        return $user->type === UserTypes::INSTITUTION;
    }

    /**
     * Determine whether the admin can bulk delete users.
     */
    public function deleteAny(Admin|User $user): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('bulk delete users');
        }

        return $user->type === UserTypes::INSTITUTION;
    }

    /**
     * Determine whether the admin can export users.
     */
    public function export(Admin|User $user): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('export users');
        }

        return $user->type === UserTypes::INSTITUTION;
    }

    /**
     * Determine whether the admin can import users.
     */
    public function import(Admin|User $user): bool
    {
        // Admin access control
        if ($user instanceof Admin) {
            return $user->hasRole('admin') || $user->checkPermissionTo('import users');
        }

        return $user->type === UserTypes::INSTITUTION;
    }

    /**
     * Check if users belong to the same institute.
     */
    private function sameInstitute(User $user, User $model): bool
    {
        return $user->institute()->intersect($model->institute())->isNotEmpty();
    }

    /**
     * Check if teacher can view a specific user.
     */
    private function canTeacherViewUser(User $user, User $model): bool
    {
        // Teachers can view students in their classrooms and other teachers in the same institute
        if ($model->type === UserTypes::STUDENT) {
            return $user->class_rooms()->intersect($model->class_rooms())->isNotEmpty();
        }

        if ($model->type === UserTypes::TEACHER) {
            return $this->sameInstitute($user, $model);
        }

        return false;
    }

    /**
     * Check if institution can manage a specific user.
     */
    private function canInstitutionManageUser(User $user, User $model): bool
    {
        // Institution owners can only manage users within their institute
        // and cannot manage other institutions
        if ($model->type === UserTypes::INSTITUTION) {
            return false;
        }

        return $this->sameInstitute($user, $model);
    }
}
