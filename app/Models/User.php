<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Models\Contracts\HasTenants;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\HasApiTokens;
use Modules\Billing\Models\Order;
use Modules\Institute\Concerns\UserTypes;
use Modules\Institute\Models\ClassRoom;
use Modules\Institute\Models\Institute;
use Modules\Subscription\Concerns\HasSubscriptionEntitlements;

class User extends Authenticatable implements FilamentUser, HasAvatar, HasTenants
{
    use Has<PERSON><PERSON>Tokens, HasFactory, HasSubscriptionEntitlements, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'username',
        'password',
        'type',
        'institute_id',
        'phone',
        'country',
        'photo',
        //        'about_me',
        'gender',
        'dob',
        'address1',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'type' => UserTypes::class,
    ];

    protected static function booted(): void
    {
        static::creating(function (User $user) {
            $user->type ??= UserTypes::STUDENT;
            $user->username ??= explode('@', $user->email)[0].'00'.$user->id;
            $user->institute_id ??= 1;
        });
    }

    public function isOwner(): bool
    {
        return $this->institute_id == $this->institute->user_id;
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function institute(): BelongsToMany
    {
        return $this->belongsToMany(Institute::class);
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    public function canAccessTenant(Model $tenant): bool
    {
        return $this->institute()->whereKey($tenant)->exists();
    }

    public function getTenants(Panel $panel): array|Collection
    {
        return $this->institute;
    }

    public function class_rooms(): BelongsToMany
    {
        return $this->belongsToMany(ClassRoom::class);
    }

    //    public function enrollment(): HasMany
    //    {
    //        return $this->hasMany(Enrollment::class);
    //    }

    public function getFilamentAvatarUrl(): ?string
    {
        // cache the image for 1 day
        $cacheKey = 'user_avatar_'.$this->id;
        $cacheDuration = now()->addDays(30); // Cache for 1 day

        return Cache::remember($cacheKey, $cacheDuration, function () {
            $url = 'https://api.dicebear.com/9.x/glass/svg?seed='.urlencode($this->name);
            $response = Http::get($url);
            if ($response->successful()) {
                $imageContent = $response->body();
                $encodedImage = base64_encode($imageContent);
                $encodedImage = base64_encode($imageContent);

                return 'data:image/svg+xml;base64,'.$encodedImage;
            }

        });
    }
}
