<?php

declare(strict_types=1);

namespace Modules\Subscription\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Subscription\Models\Entitlement;

final class EntitlementPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any entitlement if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any entitlements');
    }

    public function view(Admin $user, Entitlement $entitlement): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view entitlements');
    }

    public function create(Admin $user): bool
    {
        // Allow creating an entitlement if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create entitlements');
    }

    public function update(Admin $user, Entitlement $entitlement): bool
    {
        // Allow updating an entitlement if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update entitlements');
    }

    public function delete(Admin $user, Entitlement $entitlement): bool
    {
        // Allow deleting an entitlement if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete entitlements');
    }

    public function restore(Admin $user, Entitlement $entitlement): bool
    {
        // Allow restoring an entitlement if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore entitlements');
    }

    public function forceDelete(Admin $user, Entitlement $entitlement): bool
    {
        // Allow force deleting an entitlement if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete entitlements');
    }
}
