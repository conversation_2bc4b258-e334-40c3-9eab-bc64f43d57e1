<?php

declare(strict_types=1);

namespace Modules\Subscription\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Subscription\Models\Plan;

final class PlanPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any plan if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any plans');
    }

    public function view(Admin $user, Plan $plan): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view plans');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a plan if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create plans');
    }

    public function update(Admin $user, Plan $plan): bool
    {
        // Allow updating a plan if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update plans');
    }

    public function delete(Admin $user, Plan $plan): bool
    {
        // Allow deleting a plan if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete plans');
    }

    public function restore(Admin $user, Plan $plan): bool
    {
        // Allow restoring a plan if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore plans');
    }

    public function forceDelete(Admin $user, Plan $plan): bool
    {
        // Allow force deleting a plan if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete plans');
    }
}
