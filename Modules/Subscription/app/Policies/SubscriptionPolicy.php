<?php

declare(strict_types=1);

namespace Modules\Subscription\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Subscription\Models\Subscription;

final class SubscriptionPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any subscription if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any subscriptions');
    }

    public function view(Admin $user, Subscription $subscription): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view subscriptions');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a subscription if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create subscriptions');
    }

    public function update(Admin $user, Subscription $subscription): bool
    {
        // Allow updating a subscription if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update subscriptions');
    }

    public function delete(Admin $user, Subscription $subscription): bool
    {
        // Allow deleting a subscription if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete subscriptions');
    }

    public function restore(Admin $user, Subscription $subscription): bool
    {
        // Allow restoring a subscription if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore subscriptions');
    }

    public function forceDelete(Admin $user, Subscription $subscription): bool
    {
        // Allow force deleting a subscription if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete subscriptions');
    }
}
