<?php

namespace Modules\Subscription\Http\Controllers;

use Exception;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Services\SubscriptionService;

class SubscriptionController extends Controller
{
    protected SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
        $this->middleware('auth');
    }

    /**
     * Display a listing of available subscription plans.
     */
    public function index()
    {
        $plans = Plan::where('is_active', true)->orderBy('sort')->get();
        $currentSubscription = Auth::user()->activeSubscription();

        return view('subscription::index', compact('plans', 'currentSubscription'));
    }

    /**
     * Show subscription plan details
     */
    public function show($id)
    {
        $plan = Plan::findOrFail($id);
        $currentSubscription = Auth::user()->activeSubscription();
        $userHasThisPlan = $currentSubscription && $currentSubscription->plan_id == $plan->id;

        return view('subscription::show', compact('plan', 'currentSubscription', 'userHasThisPlan'));
    }

    /**
     * Subscribe to a plan
     */
    public function subscribe(Request $request, $planId)
    {
        $plan = Plan::findOrFail($planId);
        $user = Auth::user();

        try {
            // Subscribe the user to the selected plan
            $subscription = $this->subscriptionService->subscribeToPlan($user, $plan);

            // If the plan is free or has a trial, activate it immediately
            if ($plan->isFree() || $plan->hasTrial()) {
                return redirect()->route('subscription.thankyou', ['subscription' => $subscription->id])
                    ->with('success', 'Your subscription has been activated.');
            }

            // Otherwise, redirect to payment page
            return redirect()->route('payment.checkout', ['subscription' => $subscription->id]);

        } catch (Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Cancel a subscription
     */
    public function cancel(Request $request, $id)
    {
        $user = Auth::user();
        $subscription = $user->subscriptions()->findOrFail($id);

        // Check if this subscription belongs to the authenticated user
        if ($subscription->user_id !== $user->id) {
            return back()->withErrors(['message' => 'You are not authorized to cancel this subscription.']);
        }

        $immediately = $request->has('immediately');
        $this->subscriptionService->cancelSubscription($subscription, $immediately);

        return redirect()->route('subscription.index')
            ->with('success', 'Your subscription has been canceled.');
    }

    /**
     * Thank you page after subscription
     */
    public function thankYou($subscriptionId)
    {
        $user = Auth::user();
        $subscription = $user->subscriptions()->findOrFail($subscriptionId);

        return view('subscription::thankyou', compact('subscription'));
    }

    /**
     * Switch to another plan
     */
    public function switchPlan(Request $request, $newPlanId)
    {
        $user = Auth::user();
        $newPlan = Plan::findOrFail($newPlanId);
        $immediately = $request->has('immediately');

        try {
            $subscription = $this->subscriptionService->switchPlan($user, $newPlan, $immediately);

            // If switching to a free plan or immediately
            if ($newPlan->isFree() || $immediately) {
                return redirect()->route('subscription.index')
                    ->with('success', 'Your plan has been changed successfully.');
            }

            // Otherwise, inform user that the change will take effect later
            return redirect()->route('subscription.index')
                ->with('success', 'Your plan will be changed at the end of your current billing period.');

        } catch (Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }
}
