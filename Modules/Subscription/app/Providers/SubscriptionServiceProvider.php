<?php

declare(strict_types=1);

namespace Modules\Subscription\Providers;

use Illuminate\Console\Scheduling\Schedule;
use Modules\Core\Providers\BaseModuleServiceProvider;
use Modules\Subscription\Console\Commands\ProcessSubscriptionsCommand;
use Modules\Subscription\Services\SubscriptionService;

final class SubscriptionServiceProvider extends BaseModuleServiceProvider
{
    protected string $name = 'Subscription';

    protected string $nameLower = 'subscription';

    /**
     * Register the service provider.
     */
    public function registerModule(): void
    {
        $this->app->bind('subscription', SubscriptionService::class);
    }

    /**
     * Get module commands.
     */
    protected function getModuleCommands(): array
    {
        return $this->app->runningInConsole() ? [
            ProcessSubscriptionsCommand::class,
        ] : [];
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        $this->app->booted(function () {
            // $schedule = $this->app->make(Schedule::class);
            // $schedule->command('subscriptions:process')->daily();
        });
    }
}
