<?php

namespace Modules\Subscription\States;

use LogicException;
use Modules\Payment\Models\Payment;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Models\Subscription;

abstract class SubscriptionState
{
    protected Subscription $subscription;

    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * Get the state identifier
     */
    abstract public function getState(): string;

    /**
     * Check if subscription is active
     */
    public function isActive(): bool
    {
        return false;
    }

    /**
     * Check if subscription is in grace period
     */
    public function isInGracePeriod(): bool
    {
        return false;
    }

    /**
     * Check if subscription is canceled
     */
    public function isCanceled(): bool
    {
        return false;
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(): bool
    {
        return false;
    }

    /**
     * Handle subscription renewal
     */
    public function renew(int $days = 30): Subscription
    {
        // Default implementation - states can override
        throw new LogicException("Cannot renew subscription in {$this->getState()} state");
    }

    /**
     * Handle subscription cancellation
     */
    public function cancel(bool $immediately = false): Subscription
    {
        // Default implementation - states can override
        throw new LogicException("Cannot cancel subscription in {$this->getState()} state");
    }

    /**
     * Switch to a new plan
     */
    public function switchPlan(Plan $newPlan, bool $immediately = false): Subscription
    {
        // Default implementation - states can override
        throw new LogicException("Cannot switch plans in {$this->getState()} state");
    }

    /**
     * Handle completed payment
     */
    public function handlePaymentCompleted(Payment $payment): void
    {
        // Default implementation - states can override
        throw new LogicException("Cannot process payment in {$this->getState()} state");
    }

    /**
     * Handle failed payment
     */
    public function handlePaymentFailed(Payment $payment, string $reason): void
    {
        // Default implementation - states can override
        throw new LogicException("Cannot process failed payment in {$this->getState()} state");
    }
}
