<?php

namespace Modules\Subscription\Filament\Resources\Entitlements\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Subscription\Filament\Resources\Entitlements\EntitlementResource;

class CreateEntitlement extends CreateRecord
{
    protected static string $resource = EntitlementResource::class;

    protected function getHeaderActions(): array
    {
        return [

        ];
    }
}
