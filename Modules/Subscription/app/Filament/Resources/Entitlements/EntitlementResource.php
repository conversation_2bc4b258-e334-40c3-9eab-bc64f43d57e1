<?php

namespace Modules\Subscription\Filament\Resources\Entitlements;

use Filament\Schemas\Schema;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Subscription\Filament\Resources\Entitlements\Pages\ListEntitlements;
use Modules\Subscription\Filament\Resources\Entitlements\Pages\CreateEntitlement;
use Modules\Subscription\Filament\Resources\Entitlements\Pages\EditEntitlement;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Subscription\Filament\Resources\EntitlementResource\Pages;
use Modules\Subscription\Models\Entitlement;

class EntitlementResource extends Resource
{
    protected static ?string $model = Entitlement::class;

    protected static ?string $slug = 'entitlements';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Billing');
    }

    public static function getLabel(): ?string
    {
        return __('Entitlement');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Entitlements');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                Textarea::make('description')
                    ->maxLength(1000)
                    ->rows(3),
                Select::make('type')
                    ->options([
                        'feature' => 'Feature',
                        'module' => 'Module',
                    ])
                    ->required()
                    ->live(),

                TextInput::make('feature_key')
                    ->maxLength(255)
                    ->helperText('Unique identifier used for checking entitlements in code')
                    ->visible(fn (callable $get) => $get('type') === 'feature')
                    ->required(fn (callable $get) => $get('type') === 'feature'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
                BadgeColumn::make('type')
                    ->colors([
                        'secondary',
                        'primary' => 'feature',
                        'warning' => 'module',
                    ]),
                TextColumn::make('feature_key')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'feature' => 'Feature',
                        'module' => 'Module',
                    ]),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListEntitlements::route('/'),
            'create' => CreateEntitlement::route('/create'),
            'edit' => EditEntitlement::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
