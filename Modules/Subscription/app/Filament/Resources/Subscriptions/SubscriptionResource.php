<?php

namespace Modules\Subscription\Filament\Resources\Subscriptions;

use Filament\Schemas\Schema;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Subscription\Filament\Resources\Subscriptions\Pages\ListSubscriptions;
use Modules\Subscription\Filament\Resources\Subscriptions\Pages\CreateSubscription;
use Modules\Subscription\Filament\Resources\Subscriptions\Pages\EditSubscription;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Modules\Subscription\Filament\Resources\SubscriptionResource\Pages;
use Modules\Subscription\Models\Subscription;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $slug = 'subscriptions';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Billing');
    }

    public static function getLabel(): ?string
    {
        return __('Subscription');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Subscriptions');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([

                Select::make('user_id')

                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                Select::make('plan_id')

                    ->relationship('plan', 'name')
                    ->required(),

                Select::make('status')

                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ])
                    ->required(),

                Select::make('payment_method')

                    ->options([
                        'stripe' => 'Stripe',
                        'paypal' => 'Paypal',
                    ])
                    ->required(),

                DatePicker::make('starts_at')

                    ->required(),

                DatePicker::make('ends_at')

                    ->required(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')

                    ->searchable()
                    ->sortable(),

                TextColumn::make('plan.name')

                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSubscriptions::route('/'),
            'create' => CreateSubscription::route('/create'),
            'edit' => EditSubscription::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
