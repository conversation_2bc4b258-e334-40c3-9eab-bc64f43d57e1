<?php

namespace Modules\Subscription\Filament\Resources\Plans;

use Filament\Schemas\Schema;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\Filter;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Subscription\Filament\Resources\Plans\Pages\ListPlans;
use Modules\Subscription\Filament\Resources\Plans\Pages\CreatePlan;
use Modules\Subscription\Filament\Resources\Plans\Pages\EditPlan;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Subscription\Filament\Resources\PlanResource\Pages;
use Modules\Subscription\Filament\Resources\Plans\RelationManagers\EntitlementsRelationManager;
use Modules\Subscription\Models\Plan;

class PlanResource extends Resource
{
    protected static ?string $model = Plan::class;

    protected static ?string $slug = 'plans';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Billing');
    }

    public static function getLabel(): ?string
    {
        return __('Plan');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Plans');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Get $get, Set $set, string $operation, ?string $old, ?string $state) {
                        if (($get('slug') ?? '') !== Str::slug($old) || $operation !== 'create') {
                            return;
                        }
                        $set('slug', Str::slug($state));
                    }),
                Hidden::make('slug')
                    ->inlineLabel()
                    ->label(__('Slug'))
                    ->alphaDash()
                    ->required()
                    ->unique(ignoreRecord: true),

                Textarea::make('description')
                    ->rows(3)
                    ->maxLength(1000),

                TextInput::make('price')
                    ->numeric()
                    ->prefix('$')
                    ->required(),
                Grid::make()
                    ->schema([
                        TextInput::make('invoice_period')
                            ->numeric()
                            ->required()
                            ->default(1),

                        Select::make('invoice_interval')
                            ->options([
                                'day' => 'Day',
                                'week' => 'Week',
                                'month' => 'Month',
                                'year' => 'Year',
                            ])
                            ->required()
                            ->default('month'),
                    ])->columns(2),
                Grid::make()
                    ->schema([
                        TextInput::make('trial_period')
                            ->numeric()
                            ->default(0),

                        Select::make('trial_interval')
                            ->options([
                                'day' => 'Day',
                                'week' => 'Week',
                                'month' => 'Month',
                                'year' => 'Year',
                            ])
                            ->default('day'),
                    ])->columns(2),
                Grid::make()
                    ->schema([
                        TextInput::make('grace_period')
                            ->numeric()
                            ->default(0),

                        Select::make('grace_interval')
                            ->options([
                                'day' => 'Day',
                                'week' => 'Week',
                                'month' => 'Month',
                                'year' => 'Year',
                            ])
                            ->default('day'),
                    ])->columns(2),
                TextInput::make('active_subscribers_limit')
                    ->numeric()
                    ->nullable()
                    ->helperText('Leave empty for unlimited subscribers'),

                Toggle::make('is_active')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('price')->money('usd'),
                TextColumn::make('billing_interval'),
                TextColumn::make('invoice_period')
                    ->formatStateUsing(fn ($record) => "{$record->invoice_period} {$record->invoice_interval}(s)"),
                ToggleColumn::make('is_active'),
            ])
            ->filters([
                Filter::make('active')
                    ->query(fn ($query) => $query->where('is_active', true)),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            EntitlementsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPlans::route('/'),
            'create' => CreatePlan::route('/create'),
            'edit' => EditPlan::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
