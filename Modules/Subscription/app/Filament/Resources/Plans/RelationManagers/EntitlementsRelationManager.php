<?php

namespace Modules\Subscription\Filament\Resources\Plans\RelationManagers;

use Filament\Schemas\Schema;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Hidden;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Actions\AttachAction;
use Filament\Forms\Components\MultiSelect;
use Filament\Actions\EditAction;
use Filament\Actions\DetachAction;
use Filament\Actions\DetachBulkAction;
use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Subscription\Models\Entitlement;

class EntitlementsRelationManager extends RelationManager
{
    protected static string $relationship = 'entitlements';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('entitlement_id')
                    ->label('Entitlement')
                    ->options(Entitlement::pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if (! $state) {
                            return;
                        }

                        $entitlement = Entitlement::find($state);
                        $set('is_module', $entitlement && $entitlement->type === 'module');
                    }),

                Hidden::make('is_module')
                    ->default(false),

                // This will be conditionally shown when entitlement type is module
                Select::make('modules')
                    ->label('Available Modules')
                    ->multiple()
                    ->options([
                        'test1' => 'test1 Module',
                        'test2' => 'test2 Module',
                        // Add your actual modules here
                    ])
                    ->hidden(fn (callable $get) => ! $get('is_module'))
                    ->helperText('Select which modules this plan has access to'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable(),

                BadgeColumn::make('type')
                    ->colors([
                        'secondary',
                        'primary' => 'feature',
                        'warning' => 'module',
                    ]),

                TextColumn::make('feature_key')
                    ->searchable(),

                TextColumn::make('pivot.access_details')
                    ->label('Access Details')
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) {
                            return '-';
                        }

                        $modules = $state['modules'] ?? [];
                        if (count($modules)) {
                            return implode(', ', $modules);
                        }

                        return json_encode($state);
                    }),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'feature' => 'Feature',
                        'module' => 'Module',
                    ]),
            ])
            ->headerActions([
                AttachAction::make()
                    ->preloadRecordSelect()
                    ->form(fn (AttachAction $action) => [
                        $action->getRecordSelect(),
                        // Only show module selector for module-type entitlements
                        MultiSelect::make('modules')
                            ->label('Available Modules')
                            ->options([
                                'test1' => 'test1 Module',
                                'test2' => 'test2 Module',
                                // Add your actual modules here
                            ])
                            ->visible(function (callable $get) {
                                $entitlementId = $get('recordId');
                                if (! $entitlementId) {
                                    return false;
                                }

                                $entitlement = Entitlement::find($entitlementId);

                                return $entitlement && $entitlement->type === 'module';
                            })
                            ->helperText('Select which modules this plan has access to'),
                    ])
                    ->using(function (Model $recordToAttach, array $data) {
                        $accessDetails = [];

                        if ($recordToAttach->type === 'module' && isset($data['modules'])) {
                            $accessDetails['modules'] = $data['modules'];
                        }
                        $this->ownerRecord->entitlements()->attach(
                            $recordToAttach,
                            ['access_details' => $accessDetails]
                        );
                    }),
            ])
            ->recordActions([
                EditAction::make()
                    ->schema(function (Model $record) {
                        $isModule = $record->type === 'module';
                        $currentModules = [];

                        if ($isModule) {
                            // Get the current selected modules from the pivot
                            $pivotData = $record->pivot->access_details ?? [];
                            $currentModules = $pivotData['modules'] ?? [];
                        }

                        return [
                            MultiSelect::make('modules')
                                ->label('Available Modules')
                                ->options([
                                    'test1' => 'test1 Module',
                                    'test2' => 'test2 Module',
                                    // Add your actual modules here
                                ])
                                ->visible($isModule)
                                ->default($currentModules)
                                ->helperText('Select which modules this plan has access to'),
                        ];
                    })
                    ->action(function (Model $record, array $data) {
                        $accessDetails = $record->pivot->access_details ?? [];

                        if ($record->type === 'module' && isset($data['modules'])) {
                            $accessDetails['modules'] = $data['modules'];
                        }

                        $record->pivot->update([
                            'access_details' => $accessDetails,
                        ]);
                    }),
                DetachAction::make(),
            ])
            ->toolbarActions([
                DetachBulkAction::make(),
            ]);
    }
}
