<?php

namespace Modules\Subscription\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Payment\Models\Payment;
use Modules\Payment\Traits\HasPayments;
use Modules\Subscription\Notifications\SubscriptionExpiringNotification;
use Modules\Subscription\States\SubscriptionState;
use Modules\Subscription\States\SubscriptionStateFactory;

class Subscription extends Model
{
    use HasPayments;

    protected $fillable = [
        'user_id',
        'plan_id',
        'starts_at',
        'ends_at',
        'expires_at',
        'grace_period_ends_at',
        'status', // active, grace_period, canceled, expired
        'payment_status', // paid, pending, failed
        'last_payment_error',
        'was_switched',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'expires_at' => 'datetime',
        'grace_period_ends_at' => 'datetime',
        'was_switched' => 'boolean',
    ];

    /**
     * The current state object for this subscription.
     */
    protected ?SubscriptionState $state = null;

    // User relationship
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Plan relationship
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the current state object
     */
    public function getState(): SubscriptionState
    {
        if ($this->state === null) {
            $this->state = SubscriptionStateFactory::makeState($this);
        }

        return $this->state;
    }

    /**
     * Set the subscription state
     */
    public function setSubscriptionState(SubscriptionState $state): void
    {
        $this->state = $state;
        $this->status = $state->getState();
    }

    /**
     * Check if subscription is active
     */
    public function isActive(): bool
    {
        return $this->getState()->isActive();
    }

    /**
     * Check if subscription is in grace period
     */
    public function isInGracePeriod(): bool
    {
        return $this->getState()->isInGracePeriod();
    }

    /**
     * Check if subscription is canceled
     */
    public function isCanceled(): bool
    {
        return $this->getState()->isCanceled();
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(): bool
    {
        return $this->getState()->isExpired();
    }

    /**
     * Scope for active subscriptions
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active')
            ->where('starts_at', '<=', now())
            ->where('ends_at', '>', now());
    }

    /**
     * Scope for subscriptions in grace period
     */
    public function scopeInGracePeriod(Builder $query): Builder
    {
        return $query->where('status', 'grace_period')
            ->where('grace_period_ends_at', '>', now());
    }

    /**
     * Scope for expired subscriptions
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->where('status', 'expired')
                ->orWhere(function ($q) {
                    $q->where('status', 'active')
                        ->where('ends_at', '<', now())
                        ->whereNull('grace_period_ends_at');
                })
                ->orWhere(function ($q) {
                    $q->where('status', 'grace_period')
                        ->where('grace_period_ends_at', '<', now());
                });
        });
    }

    /**
     * Renew the subscription
     *
     * @return $this
     */
    public function renew(int $days = 30): self
    {
        $this->getState()->renew($days);

        return $this;
    }

    /**
     * Cancel the subscription
     *
     * @return $this
     */
    public function cancel(bool $immediately = false): self
    {
        $this->getState()->cancel($immediately);

        return $this;
    }

    /**
     * Switch the subscription to a new plan
     */
    public function switchPlan(Plan $newPlan, bool $immediately = false): Subscription
    {
        return $this->getState()->switchPlan($newPlan, $immediately);
    }

    /**
     * Handle successful payment
     */
    public function handlePaymentCompleted(Payment $payment): void
    {
        $this->getState()->handlePaymentCompleted($payment);
    }

    /**
     * Handle failed payment
     */
    public function handlePaymentFailed(Payment $payment, string $reason): void
    {
        $this->getState()->handlePaymentFailed($payment, $reason);
    }

    /**
     * Check if subscription needs renewal (nearing expiration)
     */
    public function needsRenewal(int $daysBeforeExpiration = 7): bool
    {
        if (! $this->isActive()) {
            return false;
        }

        return $this->ends_at && now()->addDays($daysBeforeExpiration)->isAfter($this->ends_at);
    }

    /**
     * Send expiration notification
     */
    public function sendExpirationNotification(): void
    {
        if ($this->needsRenewal() && $this->status === 'active') {
            $this->user->notify(new SubscriptionExpiringNotification($this));
        }
    }

    /**
     * When the model is being saved, update the status based on the current state
     * This ensures consistency between the state object and the database status
     */
    protected static function booted()
    {
        static::saving(function (Subscription $subscription) {
            // If the subscription has a state object, make sure the status matches
            if ($subscription->state !== null) {
                $subscription->status = $subscription->state->getState();
            }
        });
    }
}
