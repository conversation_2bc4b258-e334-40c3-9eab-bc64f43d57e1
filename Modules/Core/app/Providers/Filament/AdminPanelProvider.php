<?php

namespace Modules\Core\Providers\Filament;

use Filament\Pages\Dashboard;
use LaraZeus\SpatieTranslatable\SpatieTranslatablePlugin;
use Filament\Pages\BasePage;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Tabs\Tab;
use Exception;
use Filament\Actions\Action;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationGroup;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;
use Filament\Widgets;
use Illuminate\Support\Facades\File;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Infolists\Components\Entry;
use Filament\Support\Components\Component;
use Filament\Support\Concerns\Configurable;
use Filament\Tables\Columns\Column;
use Filament\Tables\Filters\BaseFilter;
use Modules\Core\Filament\Auth\Login;
use Modules\Core\Filament\ModulesPluginsRegister;
use Modules\Core\Filament\Widgets\SystemHealthWidget;
use Modules\Core\Settings\GeneralSettings;
use Throwable;

class AdminPanelProvider extends PanelProvider
{
    /**
     * @throws Exception
     */
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->darkMode(false)
            ->authGuard('admin')
            ->login(Login::class)
            ->profile()
            ->font('Noto Sans Arabic')
            ->passwordReset()
            ->databaseNotifications()
            ->topNavigation(false)
            ->sidebarCollapsibleOnDesktop()
            ->globalSearch(false)
            ->colors([
                'primary' => Color::Amber,
            ])
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->plugin(new ModulesPluginsRegister())
            ->bootUsing(function (Panel $panel) {
                try {
                    $logotypeSetting = app(GeneralSettings::class)->get('site_logo');
                    $logotypeUrl = ! blank($logotypeSetting) ? Storage::url($logotypeSetting) : asset('logo.svg');
                } catch (Throwable $_) {
                    $logotypeUrl = asset('logo.svg');
                }
                $panel->brandLogo($logotypeUrl);
            })
            ->navigationGroups([
                NavigationGroup::make(__('Learning'))->collapsed(),
                NavigationGroup::make(__('Billing'))->collapsed(),
                NavigationGroup::make(__('Marketing'))->collapsed(),
                NavigationGroup::make(__('Settings'))->collapsed(),
                NavigationGroup::make(__('Access'))->collapsed(),
            ])
            ->databaseTransactions()
            ->widgets([
//                Widgets\AccountWidget::class,
                SystemHealthWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->bootUsing(function (Panel $panel) {
                $panel->plugins([
                    SpatieTranslatablePlugin::make()->defaultLocales(config('core.localization.languages', ['en'])),
                ]);
                try {
                    $logotypeSetting = app(GeneralSettings::class)->site_logo;
                    $logotypeUrl = !blank($logotypeSetting) ? Storage::url($logotypeSetting) : asset('images/logo.svg');
                    $panel->brandLogo($logotypeUrl);
                    $panel->brandLogoHeight('2.5rem');
                } catch (Throwable $_) {
                    info('Failed to load site logo: ' . $_->getMessage());
                }

            })
            ->authMiddleware([
                Authenticate::class,
            ]);
    }

    public function boot(): void
    {
        $this->translatableComponents();

        FilamentView::registerRenderHook(
            PanelsRenderHook::USER_MENU_BEFORE,
            function (): string {
                return Blade::render('<a target="_blank" href="{{ url(\'/\') }}" class="block px-4 py-2 text-sm text-gray-500" role="menuitem"><x-heroicon-o-globe-alt class="h-5 w-5" /></a>');
            }
        );

        BasePage::stickyFormActions();

    }

    protected function translatableComponents(): void
    {
        foreach ([Field::class, BaseFilter::class, Placeholder::class, Column::class, Entry::class, Section::class, Action::class, Tab::class] as $component) {
            /* @var Configurable $component */
            $component::configureUsing(function (Component $translatable): void {
                /** @phpstan-ignore method.notFound */
                $this->addKeyToTranslateJsonFile($translatable->getLabel());
                $translatable->translateLabel();
            });
        }
    }

    private function addKeyToTranslateJsonFile($getLabel): void
    {
        // loop through all the files in the resources/lang directory
        $files = glob(base_path('lang/*.json'));
        foreach ($files as $file) {
            if (File::exists($file)) {

                // Read the existing file and decode JSON to an array
                $words = json_decode(File::get($file), true);

                // Ensure $words is a valid array
                if (!is_array($words)) {
                    $words = [];
                }
            } else {
                // Initialize an empty array if the file doesn't exist
                $words = [];
            }

            if (array_key_exists($getLabel, $words)) {
                continue;
            }
            $words[$getLabel] = $getLabel;
            File::put($file, json_encode($words, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }

    }

}
