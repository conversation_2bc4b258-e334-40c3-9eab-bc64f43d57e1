<?php

namespace Modules\Core\Filament\Pages;

use Dom\Text;
use Exception;
use Filament\Actions\Action;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Repeater\TableColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Url;
use Stichoza\GoogleTranslate\GoogleTranslate;

class EditLanguageTranslations extends Page
{
    use InteractsWithForms;

    public ?string $language = 'ar';

    protected string $view = 'core::filament.pages.edit-language-translations';

    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];
    
    // Add pagination properties
    public int $perPage = 50;
    public int $currentPage = 1;
    public int $totalTranslations = 0;
    
    // Add search properties
    #[Url]
    public string $search = '';
    public array $filteredTranslations = [];

    public static function getNavigationLabel(): string
    {
        return __('Translates');
    }

    public function getHeading(): string|Htmlable
    {
        return __('Translates');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('clear_search')
                ->label('Clear Search')
                ->icon('heroicon-o-x-mark')
                ->color('gray')
                ->visible(fn () => !empty($this->search))
                ->action(function () {
                    $this->search = '';
                    $this->currentPage = 1;
                    $this->loadTranslations();
                }),
            Action::make('previous_page')
                ->label('Previous')
                ->icon('heroicon-o-chevron-left')
                ->visible(fn () => $this->currentPage > 1)
                ->action(function () {
                    $this->currentPage--;
                    $this->loadTranslations();
                }),
            Action::make('page_info')
                ->label(fn () => $this->getPageInfoLabel())
                ->disabled()
                ->color('gray'),
            Action::make('next_page')
                ->label('Next')
                ->icon('heroicon-o-chevron-right')
                ->visible(fn () => $this->currentPage < ceil($this->totalTranslations / $this->perPage))
                ->action(function () {
                    $this->currentPage++;
                    $this->loadTranslations();
                }),
            Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action('refreshTranslations'),
            Action::make('auto_translate')->color('success')->icon('heroicon-o-language')->action('auto_translate'),
            Action::make('submit')->color('success')->icon('heroicon-o-check')->action('submit'),
        ];
    }

    protected function getPageInfoLabel(): string
    {
        $totalPages = ceil($this->totalTranslations / $this->perPage);
        $baseInfo = "Page {$this->currentPage} of {$totalPages} ({$this->totalTranslations} total)";
        
        if (!empty($this->search)) {
            return "Search: '{$this->search}' - {$baseInfo}";
        }
        
        return $baseInfo;
    }

    public function mount(): void
    {
        $this->language = request()->get('language', 'ar');

        // Load existing translations with pagination
        $this->loadTranslations();
    }

    protected function loadTranslations(): void
    {
        $translations = [];
        $path = lang_path("{$this->language}.json");

        // if language file does not exist, clone English version
        if (!File::exists($path)) {
            File::copy(lang_path('en.json'), $path);
        }

        if (File::exists($path)) {
            try {
                // Read and parse JSON more efficiently
                $content = File::get($path);
                $allTranslations = json_decode($content, true) ?? [];
                
                // Apply search filter if search term exists
                if (!empty($this->search)) {
                    $searchTerm = strtolower($this->search);
                    $allTranslations = array_filter($allTranslations, function ($value, $key) use ($searchTerm) {
                        return str_contains(strtolower($key), $searchTerm) || 
                               str_contains(strtolower($value), $searchTerm);
                    }, ARRAY_FILTER_USE_BOTH);
                }
                
                $this->totalTranslations = count($allTranslations);
                
                // Reset to page 1 if current page exceeds available pages after search
                $maxPage = max(1, ceil($this->totalTranslations / $this->perPage));
                if ($this->currentPage > $maxPage) {
                    $this->currentPage = 1;
                }
                
                // Apply pagination to reduce memory usage
                $offset = ($this->currentPage - 1) * $this->perPage;
                $paginatedTranslations = array_slice($allTranslations, $offset, $this->perPage, true);
                
                // Convert to Repeater format
                foreach ($paginatedTranslations as $key => $value) {
                    $translations[] = [
                        'key' => $key,
                        'value' => $value,
                    ];
                }
                
                // Clear memory
                unset($allTranslations, $paginatedTranslations, $content);
                
            } catch (Exception $e) {
                Notification::make()
                    ->title('Error loading translations: ' . $e->getMessage())
                    ->danger()
                    ->send();
                return;
            }
        }

        // Set the data property since form uses statePath('data')
        $this->data = [
            'keys' => $translations,
        ];
        
        // Fill the form with existing data
        $this->form->fill($this->data);
    }

    //    public function updatedInteractsWithForms(string $statePath): void
    //    {
    //        if($statePath == 'language') {
    //            $this->form->fill();
    //        }
    //    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make(__('Translations Keys (Language: :language)', ['language' => $this->language]))
                    ->description($this->getSearchDescription())
                    ->schema([
                        TextInput::make('search')
                            ->label('Search translations')
                            ->placeholder('Search in keys or values...')
                            ->default($this->search)
                            ->live(debounce: 500)
                            ->afterStateUpdated(function ($state) {
                                $this->search = $state ?? '';
                                $this->currentPage = 1; // Reset to first page on search
                                $this->loadTranslations();
                            })
                            ->suffixIcon('heroicon-o-magnifying-glass')
                            ->columnSpanFull(),
                        Repeater::make('keys')
                            ->table([
                                TableColumn::make('Key'),
                                TableColumn::make('Translation Value'),
                            ])
                            ->schema([
                                TextInput::make('key')
                                    ->required()
                                    ->readonly(), // Make keys readonly to prevent duplicate key issues
                                TextInput::make('value')
                                    ->required(),
                            ])
                            ->addable(false) // Disable adding new items to prevent memory issues
                            ->deletable(false) // Disable deleting items
                            ->reorderable(false) // Disable reordering
                    ])
                    ->columns(1),
            ])->statePath('data');
    }

    protected function getSearchDescription(): string
    {
        if (empty($this->search)) {
            return "Showing {$this->perPage} translations per page. Page {$this->currentPage} of " . ceil($this->totalTranslations / $this->perPage);
        }
        
        $totalPages = ceil($this->totalTranslations / $this->perPage);
        return "Found {$this->totalTranslations} results for '{$this->search}'. Page {$this->currentPage} of {$totalPages}";
    }

    public function submit(): void
    {
        try {
            // Get the current form state
            $formData = $this->form->getState();

            // Only save if we have translations to avoid empty file
            if (! blank($formData['keys'])) {
                // Load existing translations to merge with current page changes
                $path = lang_path("$this->language.json");
                $allTranslations = [];
                
                if (File::exists($path)) {
                    $content = File::get($path);
                    $allTranslations = json_decode($content, true) ?? [];
                }

                // Update only the translations from the current page
                foreach ($formData['keys'] as $item) {
                    if (! empty($item['key']) && isset($item['value'])) {
                        $allTranslations[$item['key']] = $item['value'];
                    }
                }

                // Save back to file
                File::put(
                    $path,
                    json_encode($allTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                );

                // Clear memory
                unset($allTranslations, $content);

                Notification::make()->title(__('Translation keys saved'))->success()->send();
            } else {
                Notification::make()->title('No translations to save')->warning()->send();
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Error saving translations: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function auto_translate(): void
    {
        try {
            // Get current form data
            $formData = $this->form->getState();
            
            if (empty($formData['keys'])) {
                Notification::make()->title('No keys to translate')->warning()->send();
                return;
            }

            // Extract keys from the current page only
            $keysToTranslate = collect($formData['keys'])
                ->pluck('key')
                ->filter()
                ->take(10) // Limit to 10 translations at a time to prevent memory/timeout issues
                ->toArray();
            
            if (empty($keysToTranslate)) {
                Notification::make()->title('No valid keys to translate')->warning()->send();
                return;
            }

            collect($keysToTranslate)->chunk(5)->each(function ($chunk) {
                $language = $this->language;
                dispatch(function () use ($chunk, $language) {
                    foreach ($chunk as $key) {
                        try {
                            $translated = (new GoogleTranslate)->setTarget($language)->translate($key);

                            // Read current file content to avoid overwriting
                            $path = lang_path("{$language}.json");
                            $currentData = [];
                            if (File::exists($path)) {
                                $content = File::get($path);
                                $currentData = json_decode($content, true) ?? [];
                            }

                            // Update only the translated key
                            $currentData[$key] = $translated;

                            File::put($path, json_encode($currentData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
                            
                            // Clear memory
                            unset($currentData, $content);
                            
                        } catch (Exception $e) {
                            Log::error("Translation failed for key '{$key}': " . $e->getMessage());
                        }
                    }
                });
            });
            
        Notification::make()
            ->title('Translation started for ' . count($keysToTranslate) . ' keys')
            ->body('Keys will be translated in background. Refresh the page to see updates.')
            ->success()
            ->send();
            
    } catch (Exception $exception) {
        Notification::make()->title($exception->getMessage())->danger()->send();
    }
}

public function goToPage(int $page): void
{
    $maxPage = ceil($this->totalTranslations / $this->perPage);
    $this->currentPage = max(1, min($page, $maxPage));
    $this->loadTranslations();
}

public function refreshTranslations(): void
{
    $this->loadTranslations();
    Notification::make()
        ->title('Translations refreshed')
        ->success()
        ->send();
}

public function updatedSearch(): void
{
    $this->currentPage = 1; // Reset to first page when search changes
    $this->loadTranslations();
}

public function clearSearch(): void
{
    $this->search = '';
    $this->currentPage = 1;
    $this->loadTranslations();
}
}
