<?php

namespace Modules\Core\Filament\Resources\Permissions\RelationManager;

use Filament\Schemas\Schema;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class RoleRelationManager extends RelationManager
{
    protected static string $relationship = 'roles';

    protected static ?string $recordTitleAttribute = 'name';

    protected static function getModelLabel(): string
    {
        return __('Role');
    }

    protected static function getPluralModelLabel(): string
    {
        return __('Roles');
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ,
                TextInput::make('guard_name')
                    ,

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            // Support changing table heading by translations.
            ->heading(__('Roles'))
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ,
                TextColumn::make('guard_name')
                    ->searchable()
                    ,
            ])
            ->filters([
                //
            ]);
    }
}
