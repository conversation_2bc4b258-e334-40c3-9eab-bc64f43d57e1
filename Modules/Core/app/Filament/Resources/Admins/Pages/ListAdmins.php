<?php

namespace Modules\Core\Filament\Resources\Admins\Pages;

use Filament\Actions\CreateAction;
use Modules\Core\Filament\Resources\Admins\AdminResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAdmins extends ListRecords
{
    protected static string $resource = AdminResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
