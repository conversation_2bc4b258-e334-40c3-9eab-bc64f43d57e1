<?php

namespace Modules\Core\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class SetLocaleMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if ($request->has('locale') && in_array($request->get('locale'), config('core.localization.languages'))) {
            $locale = $request->get('locale');
            Session::put('locale', $locale);
        }

        $locale = Session::get('locale');
        if (! $locale) {
            $locale = generalSetting()->site_locale ?? config('app.locale');
        }
        app()->setLocale($locale);
        return $next($request);
    }
}
