<?php
declare(strict_types=1);

namespace Modules\Core\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>ymfony\Component\Finder\Finder;
use Illuminate\Filesystem\Filesystem;

/**
 * Extract all __('...') translation keys from the codebase and ensure they exist
 * in every JSON language file under lang/.
 */
final class ExtractLangStrings extends Command
{
    protected $signature = 'lang:extract
        {--primary=en : The primary locale whose value will mirror the key}
        {--blank : Insert blank values for non-primary locales instead of copying the key}
        {--force : Overwrite existing values in non-primary locales when key newly added in primary}
        {--paths=* : Additional relative paths to scan (can repeat)}
        {--exclude=* : Additional relative paths to exclude (can repeat)}
    ';

    protected $description = 'Extract translation strings from the codebase and merge into JSON locale files.';

    private Filesystem $files;

    public function __construct(Filesystem $files)
    {
        parent::__construct();
        $this->files = $files;
    }

    public function handle(): int
    {
        $primary = (string) $this->option('primary');
        $blank = (bool) $this->option('blank');
        $force = (bool) $this->option('force');
        $extraPaths = (array) $this->option('paths');
        $extraExcludes = (array) $this->option('exclude');

        $langPath = lang_path();
        if (!$this->files->isDirectory($langPath)) {
            $this->error('lang directory not found: '.$langPath);
            return self::FAILURE;
        }

        $localeFiles = collect($this->files->files($langPath))
            ->filter(fn($f) => str_ends_with($f->getFilename(), '.json'))
            ->mapWithKeys(fn($f) => [pathinfo($f->getFilename(), PATHINFO_FILENAME) => $f->getPathname()])
            ->all();

            // dd($localeFiles);

        if (!array_key_exists($primary, $localeFiles)) {
            $this->error("Primary locale file {$primary}.json not found in lang directory.");
            return self::FAILURE;
        }

        $locales = [];
        foreach ($localeFiles as $code => $file) {
            $json = $this->files->get($file);
            $decoded = json_decode($json, true) ?? [];
            if (!is_array($decoded)) {
                $this->warn("Skipping invalid JSON file: {$file}");
                $decoded = [];
            }
            $locales[$code] = $decoded;
        }

        $this->info('Scanning project for translation keys...');

        $finder = new Finder();
        $paths = array_merge([
            base_path('app'),
            base_path('Modules'),
            base_path('resources/views'),
            base_path('themes')
        ], array_map(fn($p) => base_path($p), $extraPaths));

        $excluded = array_merge([
            'vendor', 'node_modules', 'storage', 'bootstrap/cache', 'public', 'tests', '.git'
        ], $extraExcludes);

        $finder->files()
            ->in(array_filter($paths, fn($p) => $this->files->isDirectory($p)))
            ->name(['*.php', '*.blade.php'])
            ->exclude($excluded);

    // Match __('string') or __("string") capturing string content; avoids nested parenthesis complexity.
    // Simple pattern: captures quoted literal inside __('...') or __("...") or @lang("...") or @translatable("...")

    $pattern = '/(?:__\(\s*|@(?:lang|translatable)\(\s*)(["\'])([^"\']*?)\1\s*[,)]/u';

        $keys = [];
        foreach ($finder as $file) {
            $content = $file->getContents();
            if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $m) {
                    $raw = stripcslashes($m[2]);
                    if (str_contains($raw, '{') || str_contains($raw, '$')) {
                        continue; // Skip dynamic strings
                    }
                    $trimmed = trim($raw);
                    if ($trimmed === '') {
                        continue;
                    }
                    $keys[$trimmed] = true;
                }
            }
        }

        ksort($keys, SORT_NATURAL | SORT_FLAG_CASE);
        $newCount = 0; $existing = 0;

        foreach (array_keys($keys) as $key) {
            foreach ($locales as $code => &$data) {
                if (!array_key_exists($key, $data)) {
                    if ($code === $primary) {
                        $data[$key] = $key;
                    } else {
                        $data[$key] = $blank ? '' : $key;
                    }
                    $newCount++;
                } elseif ($force && $code !== $primary) {
                    $data[$key] = $blank ? '' : $key;
                } else {
                    $existing++;
                }
            }
            unset($data); // break reference
        }
        foreach ($locales as $code => $data) {
            ksort($data, SORT_NATURAL | SORT_FLAG_CASE);
            $path = $localeFiles[$code];
            $this->files->put($path, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n");
        }

        $this->components->twoColumnDetail('Locales processed', (string) count($locales));
        $this->components->twoColumnDetail('Keys discovered', (string) count($keys));
        $this->components->twoColumnDetail('New keys added', (string) $newCount);
        $this->components->twoColumnDetail('Existing keys untouched', (string) $existing);

        $this->info('Extraction complete.');
        return self::SUCCESS;
    }
}
