<?php

namespace Modules\Core\Database\Seeders;

use <PERSON>tie\Permission\Models\Role;
use Modules\Core\Models\Admin;
use Illuminate\Database\Seeder;

class CoreDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // $this->call([]);
        // add roles some roles for admin guard
        $roles = [
            'staff' => 'Staff',
            'admin' => 'Admin',
            'editor' => 'Editor',
        ];
        foreach ($roles as $name => $displayName) {
            Role::firstOrCreate(
                ['name' => $name],
                ['guard_name' => 'admin']
            );
        }

        // add Admin User
        $adminUser = Admin::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'username' => 'admin',
                'password' => bcrypt('123123123'),
            ]
        );
        $adminUser->assignRole('admin');
    }
}
