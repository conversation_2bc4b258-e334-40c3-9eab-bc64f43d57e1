<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payment Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Payment module.
    |
    */

    // Default currency for payments
    'default_currency' => env('PAYMENT_DEFAULT_CURRENCY', 'USD'),

    // Payment gateway configurations
    'gateways' => [

        // 2Checkout configuration
        '2checkout' => [
            'api_url' => env('TWOCHECKOUT_API_URL', 'https://api.2checkout.com/rest/6.0/'),
            'enabled' => env('TWOCHECKOUT_ENABLED', true),
            'sandbox' => env('TWOCHECKOUT_SANDBOX', true),
            'seller_id' => env('TWOCHECKOUT_SELLER_ID', ''),
            'secret_key' => env('TWOCHECKOUT_SECRET_KEY', ''),
            'private_key' => env('TWOCHECKOUT_PRIVATE_KEY', ''),
            'publishable_key' => env('TWOCHECKOUT_PUBLISHABLE_KEY', ''),
            'buy_link_secret_word' => env('TWOCHECKOUT_BUY_LINK_SECRET_WORD', 'tango'),
            'icon' => '/images/payment/2checkout.png',
            'scripts' => [
                'https://2pay-js.2checkout.com/v1/2pay.js',
            ],
        ],

    ],

    // Payment logging configuration
    'logging' => [
        'channel' => env('PAYMENT_LOG_CHANNEL', 'stack'),
    ],
];
