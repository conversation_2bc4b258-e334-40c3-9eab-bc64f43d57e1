<?php

declare(strict_types=1);

namespace Modules\Payment\Gateways;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Tco\TwocheckoutFacade;
use Tco\Exceptions\TcoException;

final class TwoCheckoutGateway extends AbstractPaymentGateway
{
    private TwocheckoutFacade $tco;

    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(config('payment.gateways.2checkout'));

        // Set up 2Checkout SDK
        if ($this->isEnabled()) {
            $this->initializeTwoCheckout();
        }
    }

    /**
     * Initialize 2Checkout SDK with configuration
     */
    private function initializeTwoCheckout(): void
    {
        $config = [
            'sellerId' => $this->config['seller_id'],
            'secretKey' => $this->config['secret_key'],
            'buyLinkSecretWord' => $this->config['buy_link_secret_word'] ?? '',
            'jwtExpireTime' => 30,
            'curlVerifySsl' => $this->config['sandbox'] ? 0 : 1,
        ];
        $this->tco = new TwocheckoutFacade($config);
    }

    /**
     * Get the display name of the payment gateway
     */
    public function getName(): string
    {
        return 'TwoCheckout';
    }

    /**
     * Get the unique identifier of the payment gateway
     */
    public function getIdentifier(): string
    {
        return 'twocheckout';
    }

    /**
     * Get the description of the payment gateway
     */
    public function getDescription(): string
    {
        return 'Pay securely with your credit card via TwoCheckout';
    }

    /**
     * Process the payment
     */
    public function processPayment(array $paymentData): array
    {
        // try {
            // Prepare dynamic order parameters
            $dynamicOrderParams = [
                'Country' => $paymentData['country'] ?? 'US',
                'Currency' => strtoupper($paymentData['currency'] ?? 'USD'),
                'CustomerIP' => $paymentData['ip'] ?? request()->ip(),
                'ExternalReference' => $paymentData['reference'],
                'Language' => $paymentData['language'] ?? 'ar',
                'Source' => url('/'),
                'BillingDetails' => [
                    'Address1' => $paymentData['address']['address1'] ?? 'No address',
                    'City' => $paymentData['address']['city'] ?? 'City',
                    'State' => $paymentData['address']['state'] ?? 'State',
                    'CountryCode' => $paymentData['country'] ?? 'US',
                    'Email' => $paymentData['customer_email'],
                    'FirstName' => explode(' ', $paymentData['customer_name'])[0] ?? 'Customer',
                    'LastName' => explode(' ', $paymentData['customer_name'])[1] ?? 'Name',
                    'Zip' => $paymentData['address']['zip'] ?? '12345',
                    'Phone' => $paymentData['address']['phone'] ?? '',
                ],
                'Items' => [
                    [
                        'Name' => $paymentData['description'] ?? 'Payment',
                        'Description' => 'Payment for invoice ' . $paymentData['reference'],
                        'Quantity' => 1,
                        'IsDynamic' => true,
                        'Tangible' => false,
                        'PurchaseType' => 'PRODUCT',
                        'Price' => [
                            'Amount' => (float) $paymentData['amount'],
                            'Type' => 'CUSTOM',
                        ],
                    ],
                ],
                'PaymentDetails' => [
                    'Type' => $this->config['sandbox'] ? 'TEST' : 'CC',
                    'Currency' => strtoupper($paymentData['currency'] ?? 'USD'),
                    'CustomerIP' => $paymentData['ip'] ?? request()->ip(),
                    'PaymentMethod' => [
                        'RecurringEnabled' => false,
                        'CardType' => $paymentData['card_type'] ?? 'VISA',
                        'CardNumber' => $paymentData['card_number'] ?? '****************',
                        'ExpirationMonth' => $paymentData['expiry_month'] ?? '12',
                        'ExpirationYear' => $paymentData['expiry_year'] ?? '2030',
                        'CCID' => $paymentData['cvv'] ?? '123',
                        'HolderName' => $paymentData['cardholder_name'] ?? "John Doe",
                    ],
                ],
            ];

            // If using token payment instead of direct card details
            if (isset($paymentData['token'])) {
                $dynamicOrderParams['PaymentDetails']['Type'] = 'EES_TOKEN_PAYMENT';
                $dynamicOrderParams['PaymentDetails']['PaymentMethod'] = [
                    'EesToken' => $paymentData['token'],
                    'Vendor3DSReturnURL' => $paymentData['return_url'],
                    'Vendor3DSCancelURL' => $paymentData['cancel_url'] ?? route('payment.cancelled'),
                ];

                // Get order object and place the order
                $order = $this->tco->order();
                $response = $order->place($dynamicOrderParams);

                // dd($this->tco->apiCore()->call( '/orders/', $dynamicOrderParams, 'POST' )); // i got error msg : Hash signature could not be authenticated

                // Check if order was created successfully
                if (isset($response['RefNo'])) {
                    // Retrieve order details to verify status
                    $orderData = $this->tco->order()->getOrder(['RefNo' => $response['RefNo']]);

                    $status = $this->mapTwoCheckoutStatus($orderData['Status'] ?? 'PENDING');
                    $isSuccess = in_array($orderData['Status'] ?? '', ['AUTHRECEIVED', 'COMPLETE']);

                    return [
                        'success' => $isSuccess,
                        'message' => $isSuccess ? 'Payment processed successfully' : 'Payment is being processed',
                        'transaction_id' => $response['RefNo'],
                        'data' => array_merge($response, $orderData),
                        'status' => $status,
                        'redirect_url' => $response['PaymentDetails']['PaymentURL'] ?? null,
                    ];
                }

            }else {
                // If not using token, create a buy link
                $buyLink = $this->generateBuyLink($paymentData);
                return [
                    'success' => true,
                    'message' => 'Redirecting to payment gateway',
                    'transaction_id' => null,
                    'data' => [],
                    'status' => 'pending',
                    'redirect_url' => $buyLink,
                ];
            }


        // } catch (TcoException $e) {
        //     dd('here1',$e->getMessage()); // Debugging line, remove in production

        //     return [
        //         'success' => false,
        //         'message' => 'Payment processing failed: ' . $e->getMessage(),
        //         'status' => 'failed',
        //     ];
        // } catch (\Exception $e) {
        //     dd('here2',$e->getMessage()); // Debugging line, remove in production


        //     return [
        //         'success' => false,
        //         'message' => 'Payment processing failed',
        //         'status' => 'failed',
        //     ];
        // }
    }

    /**
     * Handle webhook notifications from the payment gateway (IPN)
     */
    public function handleWebhook(Request $request): array
    {
        try {
            $params = $request->all();
            
            // Validate required IPN parameters
            if (!isset($params['REFNOEXT']) && (!isset($params['REFNO']) || empty($params['REFNO']))) {
                throw new TcoException('Cannot identify order in IPN request.');
            }

            // Validate IPN hash
            $validIpn = $this->tco->validateIpnResponse($params);
            
            if (!$validIpn) {
                return [
                    'success' => false,
                    'message' => 'Invalid IPN signature',
                    'status' => 'failed',
                ];
            }

            // Generate IPN response token
            $responseToken = $this->tco->generateIpnResponse($params);

            // Process the IPN based on order status
            $orderStatus = $params['ORDERSTATUS'] ?? 'UNKNOWN';
            $refNo = $params['REFNO'] ?? $params['REFNOEXT'] ?? null;

            switch ($orderStatus) {
                case 'COMPLETE':
                    return [
                        'success' => true,
                        'message' => 'Payment completed successfully',
                        'transaction_id' => $refNo,
                        'data' => $params,
                        'status' => 'completed',
                        'ipn_response' => $responseToken,
                    ];

                case 'AUTHRECEIVED':
                    return [
                        'success' => true,
                        'message' => 'Payment authorized',
                        'transaction_id' => $refNo,
                        'data' => $params,
                        'status' => 'authorized',
                        'ipn_response' => $responseToken,
                    ];

                case 'PENDING':
                    return [
                        'success' => true,
                        'message' => 'Payment is pending',
                        'transaction_id' => $refNo,
                        'data' => $params,
                        'status' => 'pending',
                        'ipn_response' => $responseToken,
                    ];

                case 'CANCELED':
                case 'REFUND':
                    return [
                        'success' => false,
                        'message' => 'Payment was ' . strtolower($orderStatus),
                        'transaction_id' => $refNo,
                        'data' => $params,
                        'status' => strtolower($orderStatus),
                        'ipn_response' => $responseToken,
                    ];

                default:
                    return [
                        'success' => true,
                        'message' => 'IPN received - status: ' . $orderStatus,
                        'transaction_id' => $refNo,
                        'data' => $params,
                        'status' => 'processing',
                        'ipn_response' => $responseToken,
                    ];
            }

        } catch (TcoException $e) {
            Log::error('2Checkout IPN Error: ' . $e->getMessage(), [
                'params' => $request->all(),
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'status' => 'failed',
            ];
        } catch (Exception $e) {
            Log::error('2Checkout Webhook Error: ' . $e->getMessage(), [
                'params' => $request->all(),
            ]);

            return [
                'success' => false,
                'message' => 'Webhook processing failed',
                'status' => 'failed',
            ];
        }
    }

    /**
     * Verify the payment status
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            // Retrieve order details by reference number
            $orderData = $this->tco->order()->getOrder(['RefNo' => $paymentId]);

            $status = $this->mapTwoCheckoutStatus($orderData['Status'] ?? 'UNKNOWN');
            $isSuccess = in_array($orderData['Status'] ?? '', ['AUTHRECEIVED', 'COMPLETE']);

            if ($isSuccess) {
                return [
                    'success' => true,
                    'message' => 'Payment verified successfully',
                    'transaction_id' => $paymentId,
                    'data' => $orderData,
                    'status' => $status,
                ];
            }

            return [
                'success' => false,
                'message' => 'Payment not completed - Status: ' . ($orderData['Status'] ?? 'UNKNOWN'),
                'transaction_id' => $paymentId,
                'data' => $orderData,
                'status' => $status,
            ];

        } catch (TcoException $e) {
            Log::error('2Checkout Verification Error: ' . $e->getMessage(), [
                'payment_id' => $paymentId,
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed: ' . $e->getMessage(),
                'transaction_id' => $paymentId,
                'status' => 'failed',
            ];
        } catch (Exception $e) {
            Log::error('Payment Verification Error: ' . $e->getMessage(), [
                'payment_id' => $paymentId,
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed',
                'transaction_id' => $paymentId,
                'status' => 'failed',
            ];
        }
    }

    /**
     * Map 2Checkout status to our internal status
     */
    private function mapTwoCheckoutStatus(string $twoCheckoutStatus): string
    {
        return match ($twoCheckoutStatus) {
            'COMPLETE' => 'completed',
            'AUTHRECEIVED' => 'authorized',
            'PENDING' => 'pending',
            'CANCELED' => 'cancelled',
            'REFUND' => 'refunded',
            'REVERSED' => 'reversed',
            'FRAUD' => 'fraud',
            default => 'unknown',
        };
    }

    /**
     * Generate Buy Link for hosted checkout
     */
    public function generateBuyLink(array $paymentData): string
    {
        // try {
            $buyLinkParameters = [
                'name' => $paymentData['customer_name'],
                'email' => $paymentData['customer_email'],
                'address' => $paymentData['address']['address1'] ?? 'No address',
                'city' => $paymentData['address']['city'] ?? 'City',
                'country' => $paymentData['country'] ?? 'US',
                'state' => $paymentData['address']['state'] ?? 'State',
                'zip' => $paymentData['address']['zip'] ?? '12345',
                'phone' => $paymentData['address']['phone'] ?? '',
                'prod' => $paymentData['description'] ?? 'Paymentwall',
                'price' => (float) $paymentData['amount'],
                'qty' => 1,
                'type' => 'PRODUCT',
                'description' => 'Payment for invoice ' . $paymentData['reference'],
                'tangible' => 0,
                'src' => 'Laravel_Payment_Module',
                'return-url' => $paymentData['return_url'] ?? route('payment.success', ['order' => $paymentData['reference']]),
                'return-type' => 'redirect',
                'expiration' => time() + 3600, // 1 hour expiration
                'item-ext-ref' => '20210330102546',
                'order-ext-ref' => $paymentData['reference'],
                'customer-ext-ref' => $paymentData['customer_email'],
                'currency' => strtolower($paymentData['currency'] ?? 'USD'),
                'language' => $paymentData['language'] ?? 'en',
                'test' => $this->config['sandbox'] ? 1 : 0,
                'merchant' => $this->config['seller_id'],
                'dynamic' => 1,
            ];

            $buyLinkSignature = $this->tco->getBuyLinkSignature($buyLinkParameters);
            $buyLinkParameters['signature'] = $buyLinkSignature;

            return 'https://secure.2checkout.com/checkout/buy/?' . http_build_query($buyLinkParameters);

        // } catch (TcoException $e) {
        //     Log::error('2Checkout Buy Link Generation Error: ' . $e->getMessage());
        //     throw $e;
        // }
    }

    /**
     * Issue a refund for a payment
     */
    public function refundPayment(string $paymentId, ?float $amount = null, ?string $reason = null): array
    {
        try {
            // Get order details first
            $orderData = $this->tco->order()->getOrder(['RefNo' => $paymentId]);
            
            if (!$orderData) {
                return [
                    'success' => false,
                    'message' => 'Order not found',
                    'status' => 'failed',
                ];
            }

            // Prepare refund data
            $refundData = [
                'RefNo' => $orderData['RefNo'],
                'refundParams' => [
                    'amount' => $amount ?? (float) $orderData['GrossPrice'],
                    'comment' => $reason ?? 'Refund requested',
                    'reason' => 'Other',
                ],
            ];

            $response = $this->tco->order()->issueRefund($refundData);

            return [
                'success' => true,
                'message' => 'Refund processed successfully',
                'transaction_id' => $paymentId,
                'refund_id' => $response['RefundId'] ?? null,
                'data' => $response,
                'status' => 'refunded',
            ];

        } catch (TcoException $e) {
            Log::error('2Checkout Refund Error: ' . $e->getMessage(), [
                'payment_id' => $paymentId,
                'amount' => $amount,
            ]);

            return [
                'success' => false,
                'message' => 'Refund failed: ' . $e->getMessage(),
                'transaction_id' => $paymentId,
                'status' => 'failed',
            ];
        }
    }

    /**
     * Get frontend scripts required by this gateway
     */
    public function getScripts(): array
    {
        return [
            'https://2pay-js.2checkout.com/v1/2pay.js',
        ];
    }
}
