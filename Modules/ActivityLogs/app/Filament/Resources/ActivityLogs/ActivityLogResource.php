<?php

namespace Modules\ActivityLogs\Filament\Resources\ActivityLogs;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Placeholder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\ActivityLogs\Filament\Resources\ActivityLogs\Pages\ListActivityLogs;
use Modules\ActivityLogs\Filament\Resources\ActivityLogs\Pages\CreateActivityLog;
use Modules\ActivityLogs\Filament\Resources\ActivityLogs\Pages\EditActivityLog;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\ActivityLogs\Models\ActivityLog;

class ActivityLogResource extends Resource
{
    protected static ?string $model = ActivityLog::class;

    protected static ?string $slug = 'activity-logs';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }

    public static function getLabel(): ?string
    {
        return __('Activity Log');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Activity Logs');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make()
                    ->columnSpanFull()
                    ->schema([
                    Grid::make()
                        ->columnSpanFull()
                        ->schema([
                        TextInput::make('causer_type')->disabled()->label('User Type'),
                        TextInput::make('event')->disabled(),
                        TextInput::make('model_type')->disabled()->label('Model'),
                    ]),
                    KeyValue::make('old_values')->addable(false)->deletable(false)->label('Previous Values'),
                    KeyValue::make('new_values')->addable(false)->deletable(false)->label('Current Values'),
                ]),

                Placeholder::make('created_at')
                    ->label('Created Date')
                    ->content(fn (?ActivityLog $record): string => $record?->created_at?->diffForHumans() ?? '-'),

                Placeholder::make('updated_at')
                    ->label('Last Modified Date')
                    ->content(fn (?ActivityLog $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('causer.name')->label('User'),
                TextColumn::make('event'),
                TextColumn::make('model_type')->label('Model'),
                TextColumn::make('created_at')->dateTime(),
            ])
            ->filters([
                SelectFilter::make('event')
                    ->options([
                        'created' => 'Created',
                        'updated' => 'Updated',
                        'deleted' => 'Deleted',
                    ]),
                //
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListActivityLogs::route('/'),
            'create' => CreateActivityLog::route('/create'),
            'edit' => EditActivityLog::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
