<?php

declare(strict_types=1);

namespace Modules\ActivityLogs\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\ActivityLogs\Models\ActivityLog;

final class ActivityLogPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any activity log if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any activity logs');
    }

    public function view(Admin $user, ActivityLog $activityLog): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view activity logs');
    }

    public function create(Admin $user): bool
    {
        // Allow creating an activity log if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create activity logs');
    }

    public function update(Admin $user, ActivityLog $activityLog): bool
    {
        // Allow updating an activity log if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update activity logs');
    }

    public function delete(Admin $user, ActivityLog $activityLog): bool
    {
        // Allow deleting an activity log if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete activity logs');
    }

    public function restore(Admin $user, ActivityLog $activityLog): bool
    {
        // Allow restoring an activity log if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore activity logs');
    }

    public function forceDelete(Admin $user, ActivityLog $activityLog): bool
    {
        // Allow force deleting an activity log if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete activity logs');
    }
}
