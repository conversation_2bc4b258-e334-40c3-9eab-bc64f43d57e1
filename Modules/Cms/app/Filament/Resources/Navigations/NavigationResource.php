<?php

namespace Modules\Cms\Filament\Resources\Navigations;

use LaraZeus\SpatieTranslatable\Resources\Concerns\Translatable;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\Filter;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Cms\Filament\Resources\Navigations\Pages\ListNavigations;
use Modules\Cms\Filament\Resources\Navigations\Pages\CreateNavigation;
use Modules\Cms\Filament\Resources\Navigations\Pages\EditNavigation;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\Reactive;
use Modules\Cms\Models\Navigation;

class NavigationResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-ellipsis-horizontal';

    protected static ?string $model = Navigation::class;

    protected static ?string $slug = 'navigations';

    public static function getNavigationLabel(): string
    {
        return __('Navigations');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }


    public static function form(Schema $schema): Schema
    {

        $routeList = app('hook')->apply('navItems', [url('/') => 'Home']);

        // $routeCollection = \Route::getRoutes();
        // foreach ($routeCollection as $key => $route) {

//        foreach (TourType::all() as $tour_types) {
//            $routeList[route('tour_types.show', ['tour_types' => $tour_types])] = $tour_types->title;
//        }


        return $schema
            ->components([
                Grid::make(['default' => 1])->schema([
                    TextInput::make('key')

                        ->required()
                        ->maxLength(255),

                    Repeater::make('items')

                        ->schema([
                            Select::make('route')
                                ->helperText('if page is not found, please clear the cache from general settings')
                                ->reactive()
                                ->searchable()
                                ->afterStateUpdated(function (Select $component, Get $get, Set $set, $state) {
                                    $set('title', $component->getOptions()[$state] ?? null);
                                    $set('url', $state);
                                })
                                ->options($routeList),
                            TextInput::make('title')

                                ->required()
                                ->maxLength(255),
                            TextInput::make('url')

                                ->maxLength(255),
                            Repeater::make('items')
                                ->default([])
                                ->schema([
                                    Select::make('route')

                                        ->reactive()
                                        ->searchable()
                                        ->afterStateUpdated(function (Select $component, Get $get, Set $set, $state) {
                                            $set('title', $component->getOptions()[$state] ?? null);
                                            $set('url', $state);
                                        })
                                        ->options($routeList),
                                    TextInput::make('title')

                                        ->required()
                                        ->maxLength(255),
                                    TextInput::make('url')

                                        ->maxLength(255),
                                ])
                                ->collapsible()
                                ->itemLabel(fn (array $state): ?string => $state['title'] ?? null),
                            Toggle::make('blank')

                                ->required(),
                        ])->reorderable()
                        ->collapsible()
                        ->collapsed()
                        ->itemLabel(fn (array $state): ?string => $state['title'] ?? null),

                    Select::make('location')
                        ->options([
                            'header' => 'Header',
                            'footer' => 'Footer',
                        ])
                        ->required(),

                    Toggle::make('activated')

                        ->required(),

                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('key')->sortable(),
                ToggleColumn::make('activated')->alignRight(),
            ])
            ->filters([
                Filter::make('activated')
                    ->query(fn (Builder $query): Builder => $query->where('activated', true)),

            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListNavigations::route('/'),
            'create' => CreateNavigation::route('/create'),
            'edit' => EditNavigation::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
