<?php

namespace Modules\Cms\Filament\Resources\Navigations\Pages;

use Lara<PERSON><PERSON>\SpatieTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use Lara<PERSON><PERSON>\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Modules\Cms\Filament\Resources\Navigations\NavigationResource;

class EditNavigation extends EditRecord
{
    use Translatable;
    protected static string $resource = NavigationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            DeleteAction::make(),
        ];
    }
}
