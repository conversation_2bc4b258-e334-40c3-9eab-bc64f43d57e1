<?php

namespace Modules\Cms\Filament\Resources\Navigations\Pages;

use LaraZ<PERSON>\SpatieTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use Lara<PERSON><PERSON>\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Resources\Pages\CreateRecord;
use Modules\Cms\Filament\Resources\Navigations\NavigationResource;

class CreateNavigation extends CreateRecord
{
    use Translatable;
    protected static string $resource = NavigationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),

        ];
    }
}
