<?php

declare(strict_types=1);

namespace Modules\Cms\Filament\Resources\Posts\Pages;

use Filament\Actions\ViewAction;
use Filament\Actions\DeleteAction;
use Modules\Cms\Filament\Resources\Posts\PostResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPost extends EditRecord
{
    protected static string $resource = PostResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
