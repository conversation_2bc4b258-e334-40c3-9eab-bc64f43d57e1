<?php

declare(strict_types=1);

namespace Modules\Cms\Filament\Resources\Posts\Pages;

use Filament\Actions\CreateAction;
use Modules\Cms\Filament\Resources\Posts\PostResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPosts extends ListRecords
{
    protected static string $resource = PostResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
