<?php

declare(strict_types=1);

namespace Modules\Cms\Filament\Resources\Categories\Pages;

use Filament\Actions\ViewAction;
use Filament\Actions\DeleteAction;
use Modules\Cms\Filament\Resources\Categories\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCategory extends EditRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
