<?php

declare(strict_types=1);

namespace Modules\Cms\Filament\Resources\Pages\Pages;

use Filament\Actions\ViewAction;
use Filament\Actions\DeleteAction;
use Modules\Cms\Filament\Resources\Pages\PageResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPage extends EditRecord
{
    protected static string $resource = PageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
