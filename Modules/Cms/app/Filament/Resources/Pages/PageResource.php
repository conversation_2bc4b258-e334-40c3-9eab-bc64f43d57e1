<?php

declare(strict_types=1);

namespace Modules\Cms\Filament\Resources\Pages;

use Filament\Actions\Action;
use Filament\Actions\BulkAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Modules\Cms\Filament\Blocks\ContentBlocks;
use Modules\Cms\Filament\RichEditorBlocks\HeadingRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\ParagraphRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\ImageRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\ListRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\QuoteRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\ButtonRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\CodeRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\VideoRichBlock;
use Modules\Cms\Filament\RichEditorBlocks\CallToActionRichBlock;
use Modules\Cms\Filament\Resources\Pages\Pages\CreatePage;
use Modules\Cms\Filament\Resources\Pages\Pages\EditPage;
use Modules\Cms\Filament\Resources\Pages\Pages\ListPages;
use Modules\Cms\Filament\Resources\Pages\Pages\ViewPage;
use Modules\Cms\Models\Category;
use Modules\Cms\Models\Page;

class PageResource extends Resource
{
    protected static ?string $model = Page::class;

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-document-text';

    public static function getNavigationLabel(): string
    {
        return __('Pages');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('CMS');
    }

    protected static ?int $navigationSort = 1;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columns(3)
                    ->columnSpanFull()
                    ->schema([
                        Group::make([

                            Section::make(__('Page Information'))
                                ->columnSpanFull()
                                ->schema([
                                    TextInput::make('title')
                                        ->required()
                                        ->maxLength(255)
                                        ->live(onBlur: true)
                                        ->afterStateUpdated(fn (string $context, $state, Set $set) => $context === 'create' ? $set('slug', Str::slug($state)) : null
                                        ),
                                    TextInput::make('slug')
                                        ->required()
                                        ->maxLength(255)
                                        ->unique(Page::class, 'slug', ignoreRecord: true)
                                        ->alphaDash(),
                                    Textarea::make('excerpt')
                                        ->maxLength(500)
                                        ->rows(3)
                                        ->columnSpanFull(),
                                    Select::make('status')
                                        ->options([
                                            'draft' => 'Draft',
                                            'published' => 'Published',
                                            'archived' => 'Archived',
                                        ])
                                        ->default('draft')
                                        ->required(),
                                    DateTimePicker::make('published_at')

                                        ->default(now()),
                                    Select::make('template')
                                        ->options([
                                            'default' => 'Default',
                                            'full-width' => 'Full Width',
                                            'sidebar' => 'With Sidebar',
                                            'landing' => 'Landing Page',
                                        ])
                                        ->default('default'),
                                ])
                                ->columnSpan(1)
                                ->columns(2),

                            Section::make(__('Page Content'))
                                ->columnSpanFull()
                                ->schema([
                                    ContentBlocks::makeRichEditorForPages('content'),
                                ]),

                            Section::make(__('SEO'))
                                ->columnSpanFull()
                                ->schema([
                                    TextInput::make('meta_title')
                                        ->maxLength(60)
                                        ->helperText('Recommended: 50-60 characters'),
                                    Textarea::make('meta_description')
                                        ->maxLength(160)
                                        ->rows(3)
                                        ->helperText('Recommended: 150-160 characters'),
                                    Textarea::make('meta_keywords')
                                        ->maxLength(255)
                                        ->helperText('Comma-separated keywords'),
                                ])
                                ->columns(1)
                                ->collapsed(),

                        ])->columnSpan(2),
                        Group::make([
                            Section::make(__('Organization'))
                                ->columnSpanFull()
                                ->schema([
                                    Select::make('parent_id')

                                        ->relationship('parent', 'title')
                                        ->searchable()
                                        ->preload(),
                                    Select::make('category_id')

                                        ->relationship('category', 'name')
                                        ->searchable()
                                        ->createOptionForm([
                                            TextInput::make('name')
                                                ->required()
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(fn ($state, Set $set) => $set('slug', Str::slug($state))
                                                ),
                                            TextInput::make('slug')
                                                ->required()
                                                ->unique(Category::class, 'slug'),
                                            Textarea::make('description'),
                                        ]),
                                    TextInput::make('sort_order')
                                        ->numeric()
                                        ->default(0),
                                    Toggle::make('is_featured'),
                                ])
                                ->columns(1),

                            Section::make(__('MediaFile'))
                                ->columnSpanFull()
                                ->schema([
                                    FileUpload::make('featured_image')
                                        ->image()
                                        ->directory('cms/pages')
                                        ->visibility('public')
                                        ->imageEditor()
                                        ->columnSpanFull(),
                                ]),

                        ])->columnSpan(1),

                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('slug')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'draft',
                        'success' => 'published',
                        'danger' => 'archived',
                    ]),
                TextColumn::make('category.name')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('parent.title')

                    ->toggleable(),
                IconColumn::make('is_featured')
                    ->boolean()
                    ->toggleable(),
                TextColumn::make('published_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('sort_order')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'published' => 'Published',
                        'archived' => 'Archived',
                    ]),
                SelectFilter::make('category')
                    ->relationship('category', 'name'),
                Filter::make('is_featured')
                    ->query(fn (Builder $query): Builder => $query->where('is_featured', true)),
                TrashedFilter::make(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
                Action::make('publish')
                    ->icon('heroicon-o-eye')
                    ->action(function (Page $record) {
                        $record->update([
                            'status' => 'published',
                            'published_at' => $record->published_at ?? now(),
                        ]);
                    })
                    ->requiresConfirmation()
                    ->visible(fn (Page $record) => $record->status !== 'published'),
                Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (Page $record) {
                        $data = $record->toArray();
                        unset($data['id'], $data['created_at'], $data['updated_at'], $data['deleted_at']);
                        $data['title'] = $data['title'].' (Copy)';
                        $data['slug'] = Str::slug($data['title']);
                        $data['status'] = 'draft';
                        $data['published_at'] = null;
                        Page::create($data);
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                    BulkAction::make('publish')
                        ->icon('heroicon-o-eye')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update([
                                    'status' => 'published',
                                    'published_at' => $record->published_at ?? now(),
                                ]);
                            });
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->reorderable('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPages::route('/'),
            'create' => CreatePage::route('/create'),
            'view' => ViewPage::route('/{record}'),
            'edit' => EditPage::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
