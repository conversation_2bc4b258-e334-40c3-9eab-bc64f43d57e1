<?php

namespace Modules\Billing\Filament\Resources\Orders;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Actions\ViewAction;
use Filament\Actions\EditAction;
use Filament\Actions\ActionGroup;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\CreateAction;
use Modules\Billing\Filament\Resources\Orders\Pages\ListOrders;
use Modules\Billing\Filament\Resources\Orders\Pages\CreateOrder;
use Modules\Billing\Filament\Resources\Orders\Pages\EditOrder;
use Modules\Billing\Filament\Resources\Orders\Pages\ViewOrder;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Modules\Billing\Enums\OrderStatus;
use Modules\Billing\Filament\Resources\OrderResource\Pages;
use Modules\Billing\Models\Order;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $slug = 'billing/orders';

    protected static ?string $recordTitleAttribute = 'order_number';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-shopping-cart';

    public static function getNavigationLabel(): string
    {
        return __('Orders');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Billing');
    }

    protected static ?int $navigationSort = 10;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Group::make()
                    ->columnSpanFull()
                    ->schema([
                        Section::make('Order Information')
                            ->columnSpanFull()
                            ->schema([
                                TextInput::make('order_number')
                                    ->required()
                                    ->disabled()
                                    ->unique(ignoreRecord: true),
                                Select::make('status')
                                    ->options([
                                        OrderStatus::PENDING->value => 'Pending',
                                        OrderStatus::PROCESSING->value => 'Processing',
                                        OrderStatus::COMPLETED->value => 'Completed',
                                        OrderStatus::CANCELLED->value => 'Cancelled',
                                        OrderStatus::REFUNDED->value => 'Refunded',
                                    ])
                                    ->required(),
                                Select::make('user_id')
                                    ->relationship('user', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                TextInput::make('currency')
                                    ->required()
                                    ->length(3),
                                DateTimePicker::make('paid_at')
                                    ->label('Payment Date'),
                                TextInput::make('payment_gateway')
                                    ->label('Payment Method'),
                            ]),

                        Section::make('Financial Details')
                            ->columnSpanFull()
                            ->schema([
                                TextInput::make('subtotal')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),
                                TextInput::make('discount_total')
                                    ->numeric()
                                    ->minValue(0),
                                TextInput::make('tax')
                                    ->numeric()
                                    ->minValue(0),
                                TextInput::make('total')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),
                            ]),
                    ]),

                Group::make()
                    ->columnSpanFull()
                    ->schema([
                        Section::make('Notes')
                            ->columnSpanFull()
                            ->schema([
                                Textarea::make('note')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Section::make('Addresses')
                            ->columnSpanFull()
                            ->schema([
                                Textarea::make('billing_address')
                                    ->columnSpanFull(),
                                Textarea::make('shipping_address')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Section::make('Metadata')
                            ->schema([
                                Textarea::make('meta')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('order_number')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->sortable()
                    ->badge(),
                TextColumn::make('total')
                    ->sortable()
                    ->money(fn ($record) => $record->currency),
                TextColumn::make('paid_at')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'processing' => 'Processing',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                        'refunded' => 'Refunded',
                    ]),
                Filter::make('paid')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('paid_at')),
                Filter::make('unpaid')
                    ->query(fn (Builder $query): Builder => $query->whereNull('paid_at')),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                ActionGroup::make([
                    Action::make('mark_as_paid')
                        ->icon('heroicon-s-check-circle')
                        ->color('success')
                        ->action(fn (Order $record) => $record->setStatus(OrderStatus::COMPLETED))
                        ->visible(fn (Order $record) => $record->status === OrderStatus::PENDING),
                ]),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateActions([
                CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListOrders::route('/'),
            'create' => CreateOrder::route('/create'),
            'edit' => EditOrder::route('/{record}/edit'),
            'view' => ViewOrder::route('/{record}'),
        ];
    }
}
