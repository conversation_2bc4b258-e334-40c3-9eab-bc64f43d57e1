<?php

namespace Modules\Billing\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Response;

class InvoiceController extends Controller
{
    /**
     * Generate and download invoice PDF
     */
    public function downloadPdf(Invoice $invoice)
    {
        // Check authorization
        if (auth()->id() !== $invoice->user_id && ! auth()->user()?->can('view', $invoice)) {
            abort(403, 'Unauthorized access');
        }

        // Generate the PDF
        $pdfPath = $invoice->generatePdf();

        // Return the PDF as a download
        return Response::download($pdfPath, "invoice_{$invoice->invoice_number}.pdf", [
            'Content-Type' => 'application/pdf',
        ]);
    }

    /**
     * Send invoice to customer via email
     */
    public function sendToCustomer(Invoice $invoice)
    {
        // Check authorization
        if (! auth()->user()?->can('manage', $invoice)) {
            abort(403, 'Unauthorized access');
        }

        // Generate PDF if it doesn't exist
        $pdfPath = $invoice->generatePdf();

        // Send invoice via email notification
        $invoice->user->notify(new InvoiceNotification($invoice, $pdfPath));

        // Redirect with success message
        return redirect()->back()->with('success', 'Invoice has been sent to the customer.');
    }

    /**
     * Handle payment success callback
     */
    public function paymentSuccess(Request $request)
    {
        $orderId = $request->get('order_id');
        $paymentId = $request->get('payment_id');

        // This would typically process the successful payment and update the order
        // For now, just display a success page
        return view('billing::payment.success', compact('orderId', 'paymentId'));
    }

    /**
     * Handle payment cancellation callback
     */
    public function paymentCancel(Request $request)
    {
        $orderId = $request->get('order_id');

        // This would handle a cancelled payment
        // For now, just display a cancellation page
        return view('billing::payment.cancel', compact('orderId'));
    }
}
