<?php

return [
    'name' => 'Billing',

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | This is the default currency used for orders and invoices.
    |
    */
    'default_currency' => env('BILLING_DEFAULT_CURRENCY', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | Tax Settings
    |--------------------------------------------------------------------------
    |
    | Configure default tax rates and settings.
    |
    */
    'default_tax_rate' => env('BILLING_DEFAULT_TAX_RATE', 0.00),
    'tax_enabled' => env('BILLING_TAX_ENABLED', true),
    'tax_included' => env('BILLING_TAX_INCLUDED', false),

    /*
    |--------------------------------------------------------------------------
    | Order Settings
    |--------------------------------------------------------------------------
    |
    | Configure order generation settings.
    |
    */
    'order_prefix' => env('BILLING_ORDER_PREFIX', 'ORD-'),

    /*
    |--------------------------------------------------------------------------
    | Company Information
    |--------------------------------------------------------------------------
    |
    | Your company information for invoices and receipts.
    |
    */
    'company_name' => env('BILLING_COMPANY_NAME', config('app.name')),
    'company_address' => env('BILLING_COMPANY_ADDRESS', '123 Business St, City, Country'),
    'company_phone' => env('BILLING_COMPANY_PHONE', '+1234567890'),
    'company_email' => env('BILLING_COMPANY_EMAIL', '<EMAIL>'),
    'company_logo' => env('BILLING_COMPANY_LOGO', '/logo.png'),
    'company_vat' => env('BILLING_COMPANY_VAT', null),

    /*
    |--------------------------------------------------------------------------
    | PDF Generation
    |--------------------------------------------------------------------------
    |
    | Configure PDF generation for invoices.
    |
    */
    'pdf_paper_size' => env('BILLING_PDF_PAPER_SIZE', 'a4'),
    'pdf_orientation' => env('BILLING_PDF_ORIENTATION', 'portrait'),

    /*
    |--------------------------------------------------------------------------
    | Payment Integration Defaults
    |--------------------------------------------------------------------------
    |
    | Default settings for payment integrations.
    |
    */
    'default_payment_gateway' => env('BILLING_DEFAULT_PAYMENT_GATEWAY', 'stripe'),
    'payment_success_redirect' => env('BILLING_PAYMENT_SUCCESS_REDIRECT', '/billing/payment/success'),
    'payment_cancel_redirect' => env('BILLING_PAYMENT_CANCEL_REDIRECT', '/billing/payment/cancel'),

    /*
    |--------------------------------------------------------------------------
    | Receipt Template
    |--------------------------------------------------------------------------
    |
    | The template to use for receipts.
    |
    */
    'receipt_template' => env('BILLING_RECEIPT_TEMPLATE', 'billing::receipts.default'),

];
