<?php

namespace Modules\Course\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\Course\Enum\ContentType;
use Modules\MediaLibrary\Models\Audio;
use Modules\MediaLibrary\Models\Video;
use Modules\Quiz\Models\Quiz;

class Material extends Model
{
    protected $with = ['audio', 'video'];

    public mixed $description;

    protected $fillable = [
        'id',
        'main',
        'title',
        'learn_key_points',
        'file',
        'type',
        'text',
        'external_link',
        'embed_code',
        'youtube_id',
        'audio_id',
        'video_id',
        'pdf_id',
        'file_size',
        'file_duration',
        'file_duration_second',
        'pdf_pages',
        'prevent_access',
        'lesson_id',
    ];

    protected $casts = [
        'main' => 'boolean',
        'prevent_access' => 'array',
        'learn_key_points' => 'array',
        'type' => ContentType::class,
        'pdf_pages' => 'array',
    ];

    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class);
    }

    public function progress(): HasOne
    {
        return $this->hasOne(Progress::class)->where('user_id', auth()->id());
    }

    public function audio(): BelongsTo
    {
        return $this->belongsTo(Audio::class);
    }

    public function video(): BelongsTo
    {
        return $this->belongsTo(Video::class);
    }

    // public function quizzes()
    // {
    //     return $this->morphMany(Quiz::class, 'quizzable');
    // }
}
