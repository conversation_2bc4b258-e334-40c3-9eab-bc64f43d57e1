<?php

namespace Modules\Course\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\LessonResource;
//use Modules\Quiz\Concerns\HasQuiz;
use Spatie\Translatable\HasTranslations;

class Lesson extends Model
{
    use /*HasQuiz, */HasTranslations;

    protected $with = ['materials'];

    public bool $autoincrement = false;

    protected string $filament_resource = LessonResource::class;

    public function getFilamentResource(): mixed
    {
        return new $this->filament_resource;
    }

    protected $fillable = [
        'id',
        'title',
        'slug',
        'description',
        'learn_key_points',
        'visible',
        'unit_id',
    ];

    public array $translatable = ['title', 'description', 'learn_key_points'];

    protected $casts = [
        'learn_key_points' => 'array',
    ];

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function materials(): HasMany
    {
        return $this->hasMany(Material::class);
    }

    public function bookmarks(): HasMany
    {
        return $this->hasMany(Bookmark::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }

    public function discussions(): HasMany
    {
        return $this->hasMany(Discussion::class);
    }

    public function notes(): HasMany
    {
        return $this->hasMany(Note::class);
    }
}
