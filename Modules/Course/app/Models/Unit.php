<?php

namespace Modules\Course\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\UnitResource;
//use Modules\Quiz\Concerns\HasQuiz;
use Spatie\Translatable\HasTranslations;

class Unit extends Model
{
    use /*HasQuiz,*/ HasTranslations;

    protected string $filament_resource = UnitResource::class;

    public function getFilamentResource(): mixed
    {
        return new $this->filament_resource;
    }

    protected $fillable = [
        'title',
        'slug',
        'description',
        'visible',
        'module_id',
    ];

    public array $translatable = ['title', 'description'];

    public function module(): BelongsTo
    {
        return $this->belongsTo(Module::class);
    }

    public function lessons(): HasMany
    {
        return $this->hasMany(Lesson::class);
    }

    public function quizzes()
    {
        return $this->morphMany(Quiz::class, 'quizzable');
    }

    public function course(): BelongsTo {}
}
