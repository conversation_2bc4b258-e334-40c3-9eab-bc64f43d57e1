<?php

namespace Modules\Course\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\ModuleResource;
use Modules\Institute\Models\Institute;
//use Modules\Quiz\Concerns\HasQuiz;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Module extends Model implements HasMedia
{
    use /*HasQuiz, */HasTranslations , InteractsWithMedia;

    protected string $filament_resource = ModuleResource::class;

    /**
     * @return string
     */
    public function getFilamentResource(): mixed
    {
        return new $this->filament_resource;
    }

    public array $translatable = ['title', 'description'];

    protected $fillable = [
        'title',
        'slug',
        'learn_key_points',
        'description',
        'visible',
        'course_id',
    ];

    protected $casts = [
        'visible' => 'boolean',
        'learn_key_points' => 'array',

    ];

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function units(): HasMany
    {
        return $this->hasMany(Unit::class);
    }

    public function institute(): HasManyThrough
    {
        return $this->hasManyThrough(Institute::class, CourseInstitute::class);
    }

    public function getMediaUrlWithFallback($collection = 'default', $conversion = '')
    {
        return $this->getFirstMediaUrl($collection, $conversion) ?: asset('logo.jpg');
    }
}
