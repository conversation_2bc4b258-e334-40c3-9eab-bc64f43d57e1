<?php

namespace Modules\Course\Actions;

use Storage;
use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\MaterialData;
use Modules\Course\Models\Lesson;
use Modules\Course\Models\Material;

class GetMaterialsAction
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $lesson)
    {
        $lesson = Lesson::find($lesson);

        if (! $lesson) {
            return APIResponse::error('Lesson not found', 404);
        }
        $materials = $lesson->materials;

        if ($request->expectsJson()) {
            $data = $materials->map(function (Material $material) {
                return new MaterialData(
                    $material->main,
                    $material->title,
                    $material->file ? Storage::temporaryUrl($material->file, now()->addMinutes(5)) : null,
                    $material->type,
                    $material->text,
                    $material->external_link,
                    $material->youtube_id,
                    $material->audio_id,
                    $material->video_id,
                );
            });

            return APIResponse::success(data: $data);
        }

        return $materials;
    }
}
