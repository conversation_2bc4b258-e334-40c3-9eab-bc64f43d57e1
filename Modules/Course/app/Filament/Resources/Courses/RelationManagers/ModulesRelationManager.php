<?php

namespace Modules\Course\Filament\Resources\Courses\RelationManagers;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Fieldset;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Actions\CreateAction;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\TextInput;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Forms;
use LaraZeus\SpatieTranslatable\Resources\RelationManagers\Concerns\Translatable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\Reactive;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\ModuleResource;
use Modules\Course\Filament\Resources\SharedSections;
use Modules\Course\Models\Module;
use Modules\Quiz\Filament\Resources\QuizResource;

class ModulesRelationManager extends RelationManager
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }
    protected static ?string $relatedResource = ModuleResource::class;

    protected static string $relationship = 'modules';

    protected static bool $isLazy = false;

    #[Reactive]
    public ?string $activeLocale = null;

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Fieldset::make('Main Details')
                    ->columns(1)
                    ->schema([
                        SharedSections::getTitleAndSlug(),

                        // TODO: Add course relationship
                        Select::make('course_id')
                            ->inlineLabel()
                            ->default($this->ownerRecord->id)
                            ->hidden()
                            ->relationship('course', 'title'),

                        Textarea::make('description')
                            ->inlineLabel(),

                        SpatieMediaLibraryFileUpload::make('cover')
                            ->inlineLabel(),

                        Toggle::make('visible')
                            ->inlineLabel(),
                    ]),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                TextColumn::make('title')
                    ->limit(50),
                ToggleColumn::make('visible')
                    ->alignEnd(),
                TextInputColumn::make('sort')->width('80px'),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
            ])
            ->recordActions([
                // Action::make('edit')->icon('heroicon-m-pencil-square')->url(fn ($record) => ModuleResource::getUrl('edit', ['record' => $record])),
                DeleteAction::make(),
                Action::make('addExam')
                    ->icon('heroicon-o-academic-cap')
                    ->schema([
                        TextInput::make('min_score')->numeric(),
                        Toggle::make('visible'),
                    ])
                    ->action(function (array $data, Module $record): void {
                        $quiz = $record->quizzes()->create($data);
                        redirect()->to(QuizResource::getUrl('edit', ['record' => $quiz]));
                    }),

            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])->reorderable('sort')->defaultSort('sort');
    }
}
