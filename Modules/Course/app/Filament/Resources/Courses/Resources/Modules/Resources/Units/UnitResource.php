<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units;

use LaraZeus\SpatieTranslatable\Resources\Concerns\Translatable;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\LessonResource;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\RelationManagers\LessonsRelationManager;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Pages\ListUnits;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Pages\CreateUnit;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Pages\EditUnit;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\ModuleResource;
use Modules\Course\Models\Module;
use Modules\Course\Models\Unit;

class UnitResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Unit::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?string $relatedResource = LessonResource::class;

    protected static ?string $parentResource = ModuleResource::class;

    protected static bool $shouldRegisterNavigation = false;

    public static function getLabel(): ?string
    {
        return __('Unit');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Units');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Main Details')
                    ->description(__('The details of the unit.'))
                    ->collapsible()
                    ->columnSpanFull()
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->schema([
                        TextInput::make('title')
                            ->inlineLabel()

                            ->required(),

                        // TODO: Add module relationship
                        Select::make('module_id')
                            ->inlineLabel()
                            ->default(request('record'))
                            ->relationship('module', 'title'),

                        Textarea::make('description')
                            ->inlineLabel(),

                        Toggle::make('visible')
                            ->inlineLabel(),

                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->limit(50),
                TextColumn::make('module.title')
                    ->limit(50)
                    ->disabledClick()
                    // ->extraAttributes(function (Unit $record) {
                    //     return [
                    //         'wire:click' => '$set("tableFilters.module_id.value", '.$record->module_id.')',
                    //         'class' => 'transition hover:text-primary-500 cursor-pointer',
                    //     ];
                    // })
                    ->sortable(),
                IconColumn::make('visible')->boolean(),
            ])
            ->filters([
                SelectFilter::make('module_id')->options(fn (): array => Module::query()->pluck('title', 'id')->all()),

            ])
            ->recordActions([
                // Action::make('add_lesson')->icon('heroicon-s-plus')->url(fn ($record) => LessonResource::getUrl('create', ['record' => $record])),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            LessonsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListUnits::route('/'),
            'create' => CreateUnit::route('/create'),
            'edit' => EditUnit::route('/{record}/edit'),
        ];
    }
}
