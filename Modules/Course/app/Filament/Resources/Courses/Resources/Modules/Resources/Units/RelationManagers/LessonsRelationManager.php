<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\RelationManagers;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Actions\CreateAction;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\TextInput;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms;
use LaraZeus\SpatieTranslatable\Resources\RelationManagers\Concerns\Translatable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\Reactive;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\LessonResource;
use Modules\Course\Filament\Resources\SharedSections;
use Modules\Course\Models\Lesson;
use Modules\Quiz\Filament\Resources\QuizResource;

class LessonsRelationManager extends RelationManager
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $relatedResource = LessonResource::class;

    protected static string $relationship = 'lessons';

    protected static bool $isLazy = false;

    #[Reactive]
    public ?string $activeLocale = null;

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Main Details')
                    ->description(__('The details of the lesson.'))
                    ->columnSpanFull()
                    ->schema([

                        SharedSections::getTitleAndSlug(),

                        Select::make('unit_id')
                            ->inlineLabel()
                            ->default($this->ownerRecord->id)
                            ->hidden()
                            ->relationship('unit', 'title')
                            ->label(__('Unit')),

                        Textarea::make('description')
                            ->label(__('Description'))
                            ->inlineLabel(),

                        Toggle::make('visible')
                            ->inlineLabel()
                            ->default(true)
                            ->label(__('Visible')),

                        SharedSections::getKeyPoints(),

                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                TextColumn::make('title')
                    ->limit(50)
                    ->label(__('Title')),
                ToggleColumn::make('visible')
                    ->label(__('Visible')),
                TextInputColumn::make('sort')->width('80px')->label(__('Sort')),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
            ])
            ->recordActions([
                DeleteAction::make(),
                EditAction::make(),
                // Action::make('addExam')
                //     ->icon('heroicon-o-academic-cap')
                //     ->schema([
                //         TextInput::make('min_score')->numeric(),
                //         Toggle::make('visible')
                //             ->label(__('Visible')),
                //     ])
                //     ->action(function (array $data, Lesson $record): void {
                //         $quiz = $record->quizzes()->create($data);
                //         redirect()->to(QuizResource::getUrl('edit', ['record' => $quiz]));
                //     }),

            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])->reorderable('sort')->defaultSort('sort');
    }
}
