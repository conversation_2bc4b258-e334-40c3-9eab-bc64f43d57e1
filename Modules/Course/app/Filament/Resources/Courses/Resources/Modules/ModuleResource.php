<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules;

use LaraZeus\SpatieTranslatable\Resources\Concerns\Translatable;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\RelationManagers\UnitsRelationManager;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Pages\ListModules;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Pages\CreateModule;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Pages\EditModule;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Course\Filament\Resources\Courses\CourseResource;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\UnitResource;
use Modules\Course\Filament\Resources\SharedSections;
use Modules\Course\Models\Course;
use Modules\Course\Models\Module;

class ModuleResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Module::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $relatedResource = UnitResource::class;

    protected static ?string $parentResource = CourseResource::class;

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?int $navigationSort = 2;

    public static function getLabel(): ?string
    {
        return __('Module');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Modules');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Main Details')
                    ->description(__('The details of the module.'))
                    ->collapsible()
                    ->columnSpanFull()
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->schema([
                        SharedSections::getTitleAndSlug(),
                        // TODO: Add course relationship
                        Select::make('course_id')
                            ->inlineLabel()
                            ->default(request('record'))
                            ->relationship('course', 'title'),

                        Textarea::make('description')

                            ->inlineLabel(),

                        SpatieMediaLibraryFileUpload::make('cover')
                            ->inlineLabel(),

                        Toggle::make('visible')
                            ->inlineLabel(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->limit(50),
                TextColumn::make('course.title')
                    ->disabledClick()
                    ->extraAttributes(function (Module $record) {
                        return [
                            'wire:click' => '$set("tableFilters.course_id.value", '.$record->course_id.')',
                            'class' => 'transition hover:text-primary-500 cursor-pointer',
                        ];
                    })
                    ->sortable()
                    ->limit(50),
                IconColumn::make('visible')->boolean(),
            ])
            ->filters([
                SelectFilter::make('course_id')->options(fn (): array => Course::query()->pluck('title', 'id')->all()),

            ])
            ->recordActions([
                // Action::make('add_unit')->icon('heroicon-s-plus')->url(fn ($record) => UnitResource::getUrl('create', ['record' => $record])),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            UnitsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListModules::route('/'),
            'create' => CreateModule::route('/create'),
            'edit' => EditModule::route('/{record}/edit'),
        ];
    }
}
