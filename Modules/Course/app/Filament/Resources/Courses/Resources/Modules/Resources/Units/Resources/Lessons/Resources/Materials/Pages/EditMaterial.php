<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Resources\Materials\Pages;

use Filament\Resources\Pages\EditRecord;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Resources\Materials\MaterialResource;

class EditMaterial extends EditRecord
{
    protected static string $resource = MaterialResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
    //    protected function mutateFormDataBeforeSave(array $data): array
    //    {
    //        return parent::mutateFormDataBeforeSave($data); // TODO: Change the autogenerated stub
    //    }

}
