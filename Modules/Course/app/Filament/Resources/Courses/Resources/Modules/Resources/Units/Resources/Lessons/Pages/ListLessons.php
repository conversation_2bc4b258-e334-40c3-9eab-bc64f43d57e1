<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Pages;

use LaraZeus\SpatieTranslatable\Resources\Pages\ListRecords\Concerns\Translatable;
use LaraZeus\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions\CreateAction;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\LessonResource;

class ListLessons extends ListRecords
{
    use Translatable;

    protected static string $resource = LessonResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            CreateAction::make(),
        ];
    }
}
