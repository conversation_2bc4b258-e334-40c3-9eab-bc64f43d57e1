<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Pages;

use LaraZeus\SpatieTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use LaraZeus\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\ModuleResource;

class EditModule extends EditRecord
{
    use Translatable;

    protected static string $resource = ModuleResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            // ...
        ];
    }

    // public function getBreadcrumbs(): array
    // {
    //     return [
    //         $this->getRecord()->course->getFilamentResource()->getUrl('edit', ['record' => $this->getRecord()->course]) => $this->getRecord()->course->title,
    //         $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]) => $this->getRecordTitle(),
    //     ];
    // }
}
