<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Pages;

use LaraZ<PERSON>\SpatieTranslatable\Resources\Pages\ListRecords\Concerns\Translatable;
use LaraZ<PERSON>\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions\CreateAction;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\ModuleResource;

class ListModules extends ListRecords
{
    use Translatable;

    protected static string $resource = ModuleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            CreateAction::make(),
        ];
    }
}
