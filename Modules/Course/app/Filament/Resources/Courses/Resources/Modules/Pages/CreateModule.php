<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Pages;

use LaraZ<PERSON>\SpatieTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use Lara<PERSON><PERSON>\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\ModuleResource;

class CreateModule extends CreateRecord
{
    use Translatable;

    protected static string $resource = ModuleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            // ...
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
