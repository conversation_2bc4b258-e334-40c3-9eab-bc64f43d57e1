<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Resources\Materials;

use Filament\Schemas\Schema;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Resources\Materials\Pages\ListMaterials;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Resources\Materials\Pages\CreateMaterial;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Resources\Materials\Pages\EditMaterial;
use Filament\Resources\Resource;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\LessonResource;
use Modules\Course\Models\Material;

class MaterialResource extends Resource
{
    protected static ?string $model = Material::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $parentResource = LessonResource::class;

    protected static ?string $recordTitleAttribute = 'title';

    public static function getLabel(): ?string
    {
        return __('Material');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Materials');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema->components([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListMaterials::route(
                '/'
            ),
            'create' => CreateMaterial::route(
                '/create'
            ),
            'edit' => EditMaterial::route(
                '/{record}/edit'
            ),
        ];
    }
}
