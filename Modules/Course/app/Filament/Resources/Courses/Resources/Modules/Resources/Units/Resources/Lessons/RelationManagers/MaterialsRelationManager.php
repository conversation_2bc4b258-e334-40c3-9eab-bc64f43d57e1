<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\RelationManagers;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Group;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\FileUpload;
use Exception;
use Log;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Actions\CreateAction;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Core\Filament\Forms\Components\Picker;
use Modules\Course\Enum\ContentType;
use Modules\Institute\Concerns\UserTypes;
use Modules\MediaLibrary\Models\Audio;
use Modules\MediaLibrary\Models\Pdf;
use Modules\MediaLibrary\Models\PdfPage;
use Modules\MediaLibrary\Models\Playlist;
use Modules\MediaLibrary\Models\Video;
use setasign\Fpdi\Fpdi;

class MaterialsRelationManager extends RelationManager
{
    protected static string $relationship = 'materials';

    protected static bool $isLazy = false;

    public function form(Schema $schema): Schema
    {
        return $schema->components([
            Section::make('Main Details')
                ->description(__('The details of the material.'))
                ->columnSpanFull()
                ->schema([
                    Group::make([
                        ToggleButtons::make('main')
                            ->label(__('Main Material'))
                            ->boolean()
                            ->columnSpan(3)
                            ->grouped()
                            ->default(1),

                        TextInput::make('title')
                            ->columnSpan(9)
                            ->required()
                            ->label(__('Title')),
                    ])->columns(12),

                    Select::make('lesson_id')
                        ->hidden((bool) $this->ownerRecord->id)
                        ->default($this->ownerRecord->id)
                        ->relationship('lesson', 'title')
                        ->label(__('Lesson')),

                    Select::make('type')
                        ->label(__('Type'))
                        ->default('text')
                        ->live()
                        ->options(ContentType::class),

                    ToggleButtons::make('prevent_access')
                        ->multiple()
                        ->grouped()
                        ->inline()
                        ->options(UserTypes::class)
                        ->label(__('Prevent Access')),
                ]),

            Section::make('Material content')
                ->description(__('Content of the material.'))
                ->columnSpanFull()
                ->schema([
                    MarkdownEditor::make('text')
                        ->hidden(fn ($get): bool => $get('type') != 'text')
                        ->label(__('Text')),

                    FileUpload::make('file')
                        ->label(__('File Path'))
                        ->hidden(
                            fn ($get): bool => ! in_array($get('type'), ['video'])
                        ),

                    Select::make('pdf_id')
                        ->options(Pdf::all()->pluck('name', 'id')->toArray())
                        ->default(Pdf::first()->id)
                        ->searchable()
                        ->live()
                        ->hidden(fn ($get): bool => $get('type') != 'pdf'),

                    Select::make('audio_id')
                        ->searchable()
                        ->options(
                            Audio::all()
                                // ->merge(Playlist::all())
                                ->pluck('name', 'id')
                                ->toArray()
                        )
                        ->live()
                        ->hidden(fn ($get): bool => $get('type') != 'audio'),

                    Select::make('video_id')
                        ->searchable()
                        ->options(
                            Video::all()
                                ->pluck('name', 'id')
                                ->toArray()
                        )
                        ->live()
                        ->hidden(fn ($get): bool => $get('type') != 'video'),

                    //                    Forms\Components\Select::make("audio_id")
                    //                        ->searchable()
                    //                        ->options(
                    //                            Audio::all()
                    //                                ->merge(Playlist::all())
                    //                                ->pluck("name", "id")
                    //                                ->toArray()
                    //                        )
                    //                        ->reactive()
                    //                        ->hidden(fn($get): bool => $get("type") != "audio"),

                    Picker::make('pdf_pages')
                        ->options(fn (callable $get) => Pdf::find($get('pdf_id'))
                            ?->pdf_pages->sortBy('page_number')->pluck(
                                'page_number',
                                'id'
                            )->toArray() ?? [])
                        ->imageSize(200)
                        ->multiple()
                        ->after(function ($state, callable $set, callable $get) {
                            // Get the current path stored in external_link before potentially overwriting it
                            $oldRelativePath = $get('external_link');

                            if (empty($state)) {
                                // If selection is cleared, potentially delete the old file if it was a merged PDF
                                if ($oldRelativePath && str_starts_with($oldRelativePath, 'merged_pdfs/')) {
                                    $oldAbsolutePath = storage_path('app/public/'.$oldRelativePath);
                                    if (file_exists($oldAbsolutePath)) {
                                        @unlink($oldAbsolutePath); // Use @ to suppress errors if file is already gone
                                    }
                                    $set('external_link', null); // Clear the field
                                }

                                return;
                            }

                            $selectedPages = PdfPage::findMany($state);

                            $pagePaths = $selectedPages->pluck('page_path')->filter()->all();

                            if (count($pagePaths) < 1) {
                                // Allow single page selection to potentially replace merged file
                                // If only one page is selected, we might want to clear the merged PDF link
                                // or handle it differently depending on requirements.
                                // For now, let's clear the merged PDF link if it exists.
                                if ($oldRelativePath && str_starts_with($oldRelativePath, 'merged_pdfs/')) {
                                    $oldAbsolutePath = storage_path('app/public/'.$oldRelativePath);
                                    if (file_exists($oldAbsolutePath)) {
                                        @unlink($oldAbsolutePath);
                                    }
                                    $set('external_link', null); // Clear the field as it's no longer a merged PDF
                                }

                                return;
                            }

                            // Use FPDI to merge the PDF pages
                            $pdf = new Fpdi;
                            $pageCount = $pdf->setSourceFile(storage_path('app/public/'.$pagePaths[0]));
                            $templateId = $pdf->importPage(1);
                            // Get the size of the imported page
                            $size = $pdf->getTemplateSize($templateId);
                            $pdf = new Fpdi('P', 'mm', [$size['width'], $size['height']]);
                            foreach ($pagePaths as $path) {
                                $absolutePath = storage_path('app/public/'.$path);
                                if (file_exists($absolutePath)) {
                                    try {
                                        $pageCount = $pdf->setSourceFile($absolutePath);
                                        for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                                            $templateId = $pdf->importPage($pageNo);
                                            $pdf->AddPage();
                                            $pdf->useTemplate($templateId);
                                        }
                                    } catch (Exception $e) {
                                        Log::error("FPDI error processing page {$absolutePath}: ".$e->getMessage());
                                    }
                                } else {
                                    Log::warning("PDF page file not found: {$absolutePath}");
                                }
                            }

                            if ($pdf->PageNo() > 0) {
                                $outputDir = storage_path('app/public/merged_pdfs');
                                if (! is_dir($outputDir)) {
                                    mkdir($outputDir, 0755, true);
                                }
                                $outputFilename = 'merged_'.time().'_'.uniqid().'.pdf';
                                $outputPath = $outputDir.'/'.$outputFilename;
                                $publicPath = 'merged_pdfs/'.$outputFilename; // Relative path for DB/frontend

                                try {
                                    $pdf->Output($outputPath, 'F');

                                    // --- Deletion Logic ---
                                    // Check if there was an old merged PDF path and if it's different from the new one
                                    if ($oldRelativePath && str_starts_with($oldRelativePath, 'merged_pdfs/') && $oldRelativePath !== $publicPath) {
                                        $oldAbsolutePath = storage_path('app/public/'.$oldRelativePath);
                                        if (file_exists($oldAbsolutePath)) {
                                            @unlink($oldAbsolutePath); // Delete the old file
                                        }
                                    }
                                    // --- End Deletion Logic ---

                                    // Set the new path
                                    $set('external_link', $publicPath);

                                } catch (Exception $e) {
                                    Log::error("FPDI error writing merged PDF {$outputPath}: ".$e->getMessage());
                                    // Optionally notify the user or clear the field
                                    // $set('external_link', null);
                                }
                            } else {
                                // Handle case where no pages were added (e.g., all source files missing)
                                // Maybe clear the external_link if it previously held a merged PDF
                                if ($oldRelativePath && str_starts_with($oldRelativePath, 'merged_pdfs/')) {
                                    $oldAbsolutePath = storage_path('app/public/'.$oldRelativePath);
                                    if (file_exists($oldAbsolutePath)) {
                                        @unlink($oldAbsolutePath);
                                    }
                                    $set('external_link', null);
                                }
                            }

                        })
                        ->hidden(fn ($get): bool => $get('type') != 'pdf'),

                    //                    Forms\Components\Select::make("pdf_pages")
                    //                        ->options(
                    //                            fn(callable $get) => $get("pdf_id")
                    //                                ? Pdf::find($get("pdf_id"))
                    //                                    ->pdf_pages->sortBy('page_number')->pluck(
                    //                                    "page_number",
                    //                                    "id"
                    //                                )
                    //                                : []
                    //                        )
                    //                        ->searchable()
                    //                        ->live()
                    //                        //                            ->disabled(fn (callable $get) => !$get('pdf_id'))
                    //                        ->hidden(fn($get): bool => $get("type") != "pdf"),

                    TextInput::make('external_link')
                        ->label(__('External Link'))
                        ->hidden(
                            fn ($get): bool => ! in_array($get('type'), [
                                'youtube',
                                'pdf',
                                'embed',
                            ])
                        ),

                    Textarea::make('embed_code')
                        ->label(__('Embed Code'))
                        ->hidden(
                            fn ($get): bool => ! in_array($get('type'), [
                                'embed',
                                'vimeo',
                            ])
                        ),

                    TextInput::make('youtube_id')
                        ->label(__('Youtube ID'))
                        ->hidden(fn ($get): bool => $get('type') != 'youtube'),
                ]),
            Section::make('optional Details')
                ->description(__('optional details of the material.'))
                ->columnSpanFull()
                ->hidden(
                    fn ($get): bool => ! in_array($get('type'), [
                        'video',
                        'youtube',
                    ])
                )
                ->schema([
                    Group::make([
                        TextInput::make('file_size')
                            ->label(__('File Size'))
                            ->numeric()
                            ->prefix('kb'),

                        TextInput::make('file_duration')
                            ->label(__('File Duration'))
                            ->prefix('minutes')
                            ->numeric(),

                        TextInput::make('file_duration_second')
                            ->label(__('File Duration Second'))
                            ->prefix('seconds')
                            ->numeric(),
                    ])->columns(3),
                ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                TextColumn::make('title')->label(__('Title')),

                TextColumn::make('lesson.title')
                    ->limit(50)
                    ->label(__('Lesson')),

                TextColumn::make('type'),

                ToggleColumn::make('visible')->label(
                    __('Visible')
                ),

                TextInputColumn::make('sort')
                    ->width('80px')
                    ->label(__('Sort')),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()->modalWidth('6xl'),
            ])
            ->recordActions([
                EditAction::make()->modalWidth('6xl'),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->reorderable('sort')
            ->defaultSort('sort');
    }
}
