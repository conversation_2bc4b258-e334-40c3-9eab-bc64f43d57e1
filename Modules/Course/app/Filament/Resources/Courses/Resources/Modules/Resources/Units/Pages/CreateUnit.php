<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Pages;

use LaraZ<PERSON>\SpatieTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use LaraZ<PERSON>\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\UnitResource;

class CreateUnit extends CreateRecord
{
    use Translatable;

    protected static string $resource = UnitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            // ...
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
