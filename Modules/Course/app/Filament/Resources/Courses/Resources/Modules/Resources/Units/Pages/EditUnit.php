<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Pages;

use LaraZ<PERSON>\SpatieTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use LaraZ<PERSON>\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\UnitResource;

class EditUnit extends EditRecord
{
    use Translatable;

    protected static string $resource = UnitResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            // ...
        ];
    }

    // public function getBreadcrumbs(): array
    // {
    //     return [
    //         $this->getRecord()->module->course->getFilamentResource()->getUrl('edit', ['record' => $this->getRecord()->module->course]) => $this->getRecord()->module->course->title,
    //         $this->getRecord()->module->getFilamentResource()->getUrl('edit', ['record' => $this->getRecord()->module]) => $this->getRecord()->module->title,
    //         $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]) => $this->getRecordTitle(),
    //     ];
    // }
}
