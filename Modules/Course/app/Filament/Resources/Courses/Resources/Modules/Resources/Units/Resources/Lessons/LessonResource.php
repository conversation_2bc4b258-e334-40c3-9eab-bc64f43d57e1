<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons;

use LaraZeus\SpatieTranslatable\Resources\Concerns\Translatable;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\EditAction;
use Filament\Forms\Components\TextInput;
use Filament\Actions\ViewAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\RelationManagers\MaterialsRelationManager;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Pages\ListLessons;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Pages\CreateLesson;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Pages\EditLesson;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Pages\ViewLesson;
use Filament\Forms;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\Resources\Lessons\Resources\Materials\MaterialResource;
use Modules\Course\Filament\Resources\SharedSections;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\UnitResource;
use Modules\Course\Models\Lesson;
use Modules\Course\Models\Unit;

class LessonResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Lesson::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $relatedResource = MaterialResource::class;

    protected static ?string $parentResource = UnitResource::class;

    protected static ?string $recordTitleAttribute = 'title';

    public static function getLabel(): ?string
    {
        return __('Lesson');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Lessons');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make(__('Main Details'))
                    ->description(__('The details of the lesson.'))
                    ->collapsible()
                    ->columnSpanFull()
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->schema([

                        SharedSections::getTitleAndSlug(),

                        Select::make('unit_id')
                            ->inlineLabel()
                            ->default(request('record'))
                            ->relationship('unit', 'title')
                            ->label(__('Unit')),

                        Textarea::make('description')
                            ->inlineLabel()
                            ->label(__('Description')),

                        Toggle::make('visible')
                            ->inlineLabel()
                            ->default(true)
                            ->label(__('Visible')),

                        SharedSections::getKeyPoints(),

                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {

        return $table
            ->columns([
                TextColumn::make('title')
                    ->limit(50)
                    ->label(__('Title')),
                TextColumn::make('unit.title')
                    ->disabledClick()
                    ->extraAttributes(function (Lesson $record) {
                        return [
                            'wire:click' => '$set("tableFilters.unit_id.value", '.$record->unit_id.')',
                            'class' => 'transition hover:text-primary-500 cursor-pointer',
                        ];
                    })
                    ->sortable()
                    ->limit(50)
                    ->label(__('Unit')),
                IconColumn::make('visible')->boolean()
                    ->label(__('Visible')),
            ])
            ->filters([
                SelectFilter::make('unit_id')->options(fn (): array => Unit::query()->pluck('title', 'id')->all()),
            ])
            ->recordActions([
                ActionGroup::make([
                    EditAction::make(),
                    Action::make('addExam')
                        ->schema([
                            TextInput::make('min_score')->numeric(),
                            Toggle::make('visible')
                                ->label(__('Visible')),
                        ])
                        ->action(function (array $data, Lesson $record): void {
                            $record->quizzes()->create($data);
                        }),
                    ViewAction::make(),
                ]),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('title')->label(''),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            MaterialsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListLessons::route('/'),
            'create' => CreateLesson::route('/create'),
            'edit' => EditLesson::route('/{record}/edit'),
        ];
    }

    public static function canView(Model $record): bool
    {
        return false; // TODO: Change the autogenerated stub
    }
}
