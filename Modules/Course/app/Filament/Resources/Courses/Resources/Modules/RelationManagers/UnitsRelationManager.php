<?php

namespace Modules\Course\Filament\Resources\Courses\Resources\Modules\RelationManagers;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Actions\CreateAction;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Forms;
use LaraZeus\SpatieTranslatable\Resources\RelationManagers\Concerns\Translatable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\Reactive;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\Resources\Units\UnitResource;
use Modules\Course\Models\Unit;
use Modules\Quiz\Filament\Resources\QuizResource;

class UnitsRelationManager extends RelationManager
{
    use Translatable;

    protected static ?string $relatedResource = UnitResource::class;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static string $relationship = 'units';

    protected static bool $isLazy = false;

    #[Reactive]
    public ?string $activeLocale = null;

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Main Details')
                    ->columns(1)
                    ->schema([
                        TextInput::make('title')
                            ->inlineLabel()

                            ->required(),

                        // TODO: Add module relationship
                        Select::make('module_id')
                            ->inlineLabel()
                            ->default($this->ownerRecord->id)
                            ->hidden()
                            ->relationship('module', 'title'),

                        Textarea::make('description')
                            ->inlineLabel(),

                        Toggle::make('visible')
                            ->inlineLabel(),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                TextColumn::make('title'),
                TextInputColumn::make('sort')->width('80px'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
            ])
            ->recordActions([
                // Action::make('edit')->icon('heroicon-m-pencil-square')->url(fn ($record) => UnitResource::getUrl('edit', ['record' => $record])),
                DeleteAction::make(),
                // Action::make('addExam')
                //     ->icon('heroicon-o-academic-cap')
                //     ->schema([
                //         TextInput::make('min_score')->numeric(),
                //         Toggle::make('visible'),
                //     ])
                //     ->action(function (array $data, Unit $record): void {
                //         $quiz = $record->quizzes()->create($data);
                //         redirect()->to(QuizResource::getUrl('edit', ['record' => $quiz]));
                //     }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])->reorderable('sort')->defaultSort('sort');
    }
}
