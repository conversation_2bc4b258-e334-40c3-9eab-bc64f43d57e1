<?php

namespace Modules\Course\Filament\Resources\Courses;

use LaraZeus\SpatieTranslatable\Resources\Concerns\Translatable;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Fieldset;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Actions\EditAction;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Course\Filament\Resources\Courses\RelationManagers\ModulesRelationManager;
use Modules\Course\Filament\Resources\Courses\Pages\ListCourses;
use Modules\Course\Filament\Resources\Courses\Pages\EditCourse;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Course\Enum\CourseLevels;
use Modules\Course\Filament\Resources\Courses\Resources\Modules\ModuleResource;
use Modules\Course\Filament\Resources\SharedSections;
use Modules\Course\Models\Course;
use Modules\MediaLibrary\Models\Pdf;
use Modules\Quiz\Filament\Resources\QuizResource;

class CourseResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Learning');
    }

    protected static ?string $relatedResource = ModuleResource::class;

    protected static ?string $model = Course::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?int $navigationSort = -1;

    protected static ?string $recordTitleAttribute = 'title';

    public static function getLabel(): ?string
    {
        return __('Course');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Courses');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Main Details')
                    ->description(__('The details of the course.'))
                    ->collapsible(fn (string $operation): bool => $operation !== 'create')
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->columnSpanFull()
                    ->columns(12)
                    ->schema([
                        Group::make()
                            ->columnSpan(9)
                            ->schema([
                                Fieldset::make()
                                    ->columnSpanFull()
                                    ->schema([
                                        SharedSections::getTitleAndSlug(),

                                        TextInput::make('youtube_intro_video_id')
                                            ->inlineLabel()
                                            ->required(),
                                        MarkdownEditor::make('description'),
                                    ]),
                            ]),

                        Group::make()
                            ->columnSpan(3)
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('cover')
                                    ->image()
                                    ->imageEditor()
                                    ->columnSpanFull()
                                    ->hiddenLabel(),

                                Fieldset::make()
                                    ->columnSpanFull()
                                    ->schema([
                                            TextInput::make('price')
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(fn ($state, callable $set) => $set('public', false))
                                                ->numeric()
                                                ->hintIcon('heroicon-s-question-mark-circle')
                                                ->hintIconTooltip('Free if zero')
                                                ->default(0)
                                                ->rules('regex:/^\d{1,6}(\.\d{0,2})?$/')
                                                ->prefix(generalSetting()->currency)
                                                ->required(),

                                            Select::make('level')
                                                ->options(CourseLevels::class)
                                                ->required(),

                                            Toggle::make('public')
                                                ->live()
                                                ->disabled(fn (callable $get) => $get('price') != 0)
                                                ->hintIcon('heroicon-s-question-mark-circle')
                                                ->hintIconTooltip(__('Can anyone view this course even if not logged in')),

                                        Toggle::make('visible'),
                                        Toggle::make('is_featured'),
                                    ]),
                            ]),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->limit(50),
                TextColumn::make('price'),
                TextColumn::make('level'),
                IconColumn::make('visible')->boolean(),
                TextInputColumn::make('sort')->width('80px'),

            ])
            ->filters([
                //
            ])
            ->recordActions([
                //                Tables\Actions\Action::make('add_module')->icon('heroicon-s-plus')->url(fn ($record) => ModuleResource::getUrl('create', ['record' => $record])),
                EditAction::make(),
                Action::make('addExam')
                    ->icon('heroicon-o-academic-cap')
                    ->schema([
                        TextInput::make('min_score')->numeric(),
                        Toggle::make('visible'),
                    ])
                    ->action(function (array $data, Course $record): void {
                        $quiz = $record->quizzes()->create($data);
                        redirect()->to(QuizResource::getUrl('edit', ['record' => $quiz]));
                    }),
                Action::make('replace-book')->icon('heroicon-o-arrow-uturn-up')->schema(fn (Course $record) => [
                    Select::make('old_pdf')->options(Pdf::all()->pluck('name', 'id')->toArray()),
                    Select::make('new_pdf')->options(Pdf::all()->pluck('name', 'id')->toArray()),
                ])->action(function (Course $record, array $data) {
                    $material = Course::where('id', $record->id)->with('modules.units.lessons.materials')
                        ->first()
                        ->modules
                        ->flatMap(function ($module) {
                            return $module->units->flatMap(function ($unit) {
                                return $unit->lessons->flatMap(function ($lesson) {
                                    return $lesson->materials;
                                });
                            });
                        });
                    $material->where('pdf_id', $data['old_pdf'])->map(function ($pdf) use ($data) {
                        $pdf->update(['pdf_id' => $data['new_pdf']]);
                    });
                }),

            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])->reorderable('sort')->defaultSort('sort');
    }

    public static function getRelations(): array
    {
        return [
            ModulesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCourses::route('/'),
            //            'create' => \Modules\Course\Filament\Resources\CourseResource\Pages\CreateCourse::route('/create'),
            'edit' => EditCourse::route('/{record}/edit'),
        ];
    }
}
