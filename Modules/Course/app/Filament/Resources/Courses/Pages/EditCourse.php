<?php

namespace Modules\Course\Filament\Resources\Courses\Pages;

use LaraZ<PERSON>\SpatieTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use LaraZ<PERSON>\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Course\Filament\Resources\Courses\CourseResource;

class EditCourse extends EditRecord
{
    use Translatable;

    protected static string $resource = CourseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            // ...
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
