<?php

namespace Modules\Course\Filament\Resources\Courses\Pages;

use LaraZ<PERSON>\SpatieTranslatable\Resources\Pages\ListRecords\Concerns\Translatable;
use LaraZeus\SpatieTranslatable\Actions\LocaleSwitcher;
use Filament\Actions\CreateAction;
use Filament\Support\Enums\Width;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Course\Filament\Resources\Courses\CourseResource;

class ListCourses extends ListRecords
{
    use Translatable;

    protected static string $resource = CourseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            CreateAction::make()->modalWidth(Width::Full),
        ];
    }
}
