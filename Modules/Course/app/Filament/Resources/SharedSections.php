<?php

namespace Modules\Course\Filament\Resources;

use Filament\Schemas\Components\Section;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Forms;
use Illuminate\Support\Str;

class SharedSections
{
    public static function getKeyPoints(): Section
    {
        return Section::make()
            ->schema([
                Repeater::make('learn_key_points')
                    ->simple(
                        TextInput::make('key_point')
                            ->label(__('Key point'))
                            ->required(),
                    )
                    ->default([])
                    ->label(__('Learn key points')),

            ]);
    }

    /**
     * @return array
     */
    public static function getTitleAndSlug(): Group
    {
        return Group::make([
            TextInput::make('title')
                ->inlineLabel()
                ->label(__('Title'))
                ->placeholder(__('Enter a title'))
                ->live(onBlur: true)
                ->afterStateUpdated(function (Get $get, Set $set, string $operation, ?string $old, ?string $state) {
                    if (($get('slug') ?? '') !== Str::slug($old) || $operation !== 'create') {
                        return;
                    }
                    $set('slug', Str::slug($state).rand(111, 999));
                })
                ->afterStateHydrated(function (TextInput $component, $state) {
                    if (is_array($state)) {
                        // get first value of array
                        $state = array_shift($state);
                    }
                    $component->state(ucwords($state));
                })
                ->required()
                ->maxLength(255)
                ->autofocus(),

            TextInput::make('slug')
                ->inlineLabel()
                ->label(__('Slug'))
                ->alphaDash()
                ->required()
                ->unique(ignoreRecord: true),
        ]);
    }
}
