<?php

namespace Modules\Course\Filament\Widgets;

use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Billing\Models\Order;
use Modules\Course\Models\Material;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Users', User::count())
                ->description('Last 6 weeks')
                ->chart(
                    collect(range(0, 6))
                        ->map(fn ($i) => User::whereBetween('created_at', [
                            now()->startOfWeek()->subWeeks(6 - $i),
                            now()->startOfWeek()->subWeeks(6 - $i)->endOfWeek(),
                        ])->count())
                        ->toArray()
                )
                // ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),
            Stat::make('Materials', Material::count())
                ->description('Last 6 weeks')
                ->chart(
                    collect(range(0, 6))
                        ->map(fn ($i) => Material::whereBetween('created_at', [
                            now()->startOfWeek()->subWeeks(6 - $i),
                            now()->startOfWeek()->subWeeks(6 - $i)->endOfWeek(),
                        ])->count())
                        ->toArray()
                )
                // ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),
            Stat::make('Orders', Order::count())
                ->color('success')
                ->description('Last 6 weeks')
                ->chart(
                    collect(range(0, 6))
                        ->map(fn ($i) => Order::whereBetween('created_at', [
                            now()->startOfWeek()->subWeeks(6 - $i),
                            now()->startOfWeek()->subWeeks(6 - $i)->endOfWeek(),
                        ])->count())
                        ->toArray()
                ),
            Stat::make('Revenue', Order::sum('total'))
                ->color('warning')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->description('USD')
                ->chart(
                    collect(range(0, 6))
                        ->map(fn ($i) => Order::whereBetween('created_at', [
                            now()->startOfWeek()->subWeeks(6 - $i),
                            now()->startOfWeek()->subWeeks(6 - $i)->endOfWeek(),
                        ])->sum('total'))
                        ->toArray()
                ),

        ];
    }
}
