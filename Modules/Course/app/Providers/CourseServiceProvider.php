<?php

declare(strict_types=1);

namespace Modules\Course\Providers;

use Illuminate\Support\Facades\Cache;
use Modules\Core\Providers\BaseModuleServiceProvider;
use Modules\Course\Models\Course;

final class CourseServiceProvider extends BaseModuleServiceProvider
{
    protected string $name = 'Course';

    protected string $nameLower = 'course';

    /**
     * Custom boot logic for Course module.
     */
    public function bootModule(): void
    {
        // Register course navigation items
        $this->app->booted(function () {
            if (app()->runningInConsole()) {
                return;
            }

            foreach (Cache::remember('all_courses', 3600, fn () => Course::all()) as $course) {
                app('hook')->register('navItems', fn ($items) => $items + [route('course.show', ['course' => $course]) => $course->title]);
            }
        });
    }

    /**
     * Register the service provider.
     */
    public function registerModule(): void
    {

    }

    /**
     * Get module commands.
     */
    protected function getModuleCommands(): array
    {
        return [];
    }
}
