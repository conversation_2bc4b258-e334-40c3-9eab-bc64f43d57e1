<?php

namespace Modules\Course\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Course\Models\Lesson;

class LessonPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any Lesson if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any Lessons');
    }

    public function view(Admin $user, Lesson $Lesson): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view Lessons');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a Lesson if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create Lessons');
    }

    public function update(Admin $user, Lesson $Lesson): bool
    {
        // Allow updating a Lesson if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update Lessons');
    }

    public function delete(Admin $user, Lesson $Lesson): bool
    {
        // Allow deleting a Lesson if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete Lessons');
    }

    public function restore(Admin $user, Lesson $Lesson): bool
    {
        // Allow restoring a Lesson if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore Lessons');
    }

    public function forceDelete(Admin $user, Lesson $Lesson): bool
    {
        // Allow force deleting a Lesson if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete Lessons');
    }
}
