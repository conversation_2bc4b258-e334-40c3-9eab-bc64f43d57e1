<?php

namespace Modules\Course\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Course\Models\Course;

class CoursePolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any course if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any courses');
    }

    public function view(Admin $user, Course $course): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view courses');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a course if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create courses');
    }

    public function update(Admin $user, Course $course): bool
    {
        // Allow updating a course if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update courses');
    }

    public function delete(Admin $user, Course $course): bool
    {
        // Allow deleting a course if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete courses');
    }

    public function restore(Admin $user, Course $course): bool
    {
        // Allow restoring a course if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore courses');
    }

    public function forceDelete(Admin $user, Course $course): bool
    {
        // Allow force deleting a course if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete courses');
    }
}
