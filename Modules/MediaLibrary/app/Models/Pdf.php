<?php

namespace Modules\MediaLibrary\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Modules\Course\Models\Material;

class Pdf extends Model
{
    protected $fillable = [
        'name',
        'path',
    ];

    // todo: change to morph relation
    //    public function material(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    //    {
    //        return $this->belongsTo(Material::class);
    //    }

    public function pdf_pages(): HasMany
    {
        return $this->hasMany(PdfPage::class);
    }

    // delete all pages when deleting the pdf
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($pdf) {
            // Get all PDF pages before deleting them
            $pdfPages = $pdf->pdf_pages;

            // Delete each page file from storage
            foreach ($pdfPages as $page) {
                if ($page->page_path && Storage::disk('public')->exists($page->page_path)) {
                    Storage::disk('public')->delete($page->page_path);
                }
            }

            // Delete all page records from the database
            $pdf->pdf_pages()->delete();

            // Delete the original PDF file if it exists
            if ($pdf->path && Storage::disk('public')->exists($pdf->path)) {
                Storage::disk('public')->delete($pdf->path);
            }

            // Delete the PDF directory if it exists
            $originalFileNameWithoutExt = pathinfo($pdf->path, PATHINFO_FILENAME);
            $outputDir = 'pdfs/'.$originalFileNameWithoutExt;

            if (Storage::disk('public')->exists($outputDir)) {
                Storage::disk('public')->deleteDirectory($outputDir);
            }
        });
    }
}
