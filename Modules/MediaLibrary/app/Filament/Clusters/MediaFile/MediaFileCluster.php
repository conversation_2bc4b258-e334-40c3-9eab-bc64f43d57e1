<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile;

use Filament\Clusters\Cluster;

class MediaFileCluster extends Cluster
{
    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?int $navigationSort = 9;

    public static function getNavigationLabel(): string
    {
        return __('Media Files');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Learning');
    }
}
