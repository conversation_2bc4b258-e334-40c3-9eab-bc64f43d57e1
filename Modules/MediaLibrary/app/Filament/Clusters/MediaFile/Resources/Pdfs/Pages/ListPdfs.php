<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Pdfs\Pages;

use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Pdfs\PdfResource;

class ListPdfs extends ListRecords
{
    protected static string $resource = PdfResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
