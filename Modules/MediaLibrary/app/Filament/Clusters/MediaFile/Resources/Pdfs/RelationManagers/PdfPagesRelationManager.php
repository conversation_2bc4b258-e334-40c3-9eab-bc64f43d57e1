<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Pdfs\RelationManagers;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\ViewField;
use Storage;
use Filament\Tables\Columns\TextColumn;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\MediaLibrary\Models\PdfPage;

class PdfPagesRelationManager extends RelationManager
{
    protected static string $relationship = 'pdf_pages';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([

                Grid::make()
                    ->columnSpanFull()
                    ->schema([

                    Group::make([
                        TextInput::make('page_number')
                            ->required()
                            ->maxLength(255),

                        FileUpload::make('page_path')
                            ->required(),
                    ]),

                    ViewField::make('page_path')
                        ->label('Page Path')
                        ->viewData([
                            'path' => Storage::url($this->cachedMountedTableActionRecord['page_path']),
                        ])
                        ->view('medialibrary::forms.components.view-field-pdf'),
                    //                    Forms\Components\Placeholder::make('page_path')
                    //                        ->content(fn(?PdfPage $record): string => url(\Storage::url($record?->page_path)) ?? '-'),
                ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('page_number')
            ->defaultSort('page_number', 'asc')
            ->columns([
                TextColumn::make('page_number'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                //                Tables\Actions\CreateAction::make(),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
