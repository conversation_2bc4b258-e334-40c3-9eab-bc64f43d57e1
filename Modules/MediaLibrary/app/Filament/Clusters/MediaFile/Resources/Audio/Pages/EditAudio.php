<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio\Pages;

use Filament\Actions\DeleteAction;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio\AudioResource;

class EditAudio extends EditRecord
{
    protected static string $resource = AudioResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
