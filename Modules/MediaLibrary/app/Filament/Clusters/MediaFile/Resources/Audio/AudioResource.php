<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Group;
use Filament\Tables\Columns\TextColumn;
use Filament\Actions\EditAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio\Pages\ListAudio;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio\Pages\CreateAudio;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio\Pages\EditAudio;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\MediaFileCluster;
use Modules\MediaLibrary\Models\Audio;

class AudioResource extends Resource
{
    protected static ?string $model = Audio::class;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $cluster = MediaFileCluster::class;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Group::make([
                    TextInput::make('name')

                        ->required(),

                    TextInput::make('author'),

                    Select::make('playlist_id')

                        ->relationship('playlist', 'name')
                        ->createOptionForm([
                            TextInput::make('name'),
                        ]),

                ])->columns(3)->columnSpanFull(),

                FileUpload::make('path')
                    ->hiddenLabel()
                    ->columnSpan(2)
                    ->acceptedFileTypes(['audio/*'])
                    ->getUploadedFileNameForStorageUsing(
                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                            ->prepend('audio-'),
                    ),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAudio::route('/'),
            'create' => CreateAudio::route('/create'),
            'edit' => EditAudio::route('/{record}/edit'),
        ];
    }
}
