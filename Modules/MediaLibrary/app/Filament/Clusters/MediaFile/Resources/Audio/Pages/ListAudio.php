<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio\Pages;

use Filament\Actions\CreateAction;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Audio\AudioResource;

class ListAudio extends ListRecords
{
    protected static string $resource = AudioResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
