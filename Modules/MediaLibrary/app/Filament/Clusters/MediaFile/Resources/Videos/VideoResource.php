<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\Pages\ListVideos;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\Pages\CreateVideo;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\Pages\EditVideo;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\MediaFileCluster;
use Modules\MediaLibrary\Models\Video;

class VideoResource extends Resource
{
    protected static ?string $model = Video::class;

    protected static ?string $slug = 'videos';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-film';

    protected static ?string $cluster = MediaFileCluster::class;

    public static function getLabel(): ?string
    {
        return __('Video');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Videos');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([

                TextInput::make('name')
                    ->label(__('Name'))
                    ->columnSpanFull()
                    ->inlineLabel()
                    ->required(),

//                TextInput::make('duration')
//                    ->readonly()
//                    ->dehydrated(false)
//                    ->columnSpanFull()
//                    ->inlineLabel(),

                Hidden::make('minutes')
                    ->default(0),
                Hidden::make('seconds')
                    ->default(0),

                FileUpload::make('path')
                    ->label(__('Path'))
                    ->columnSpan(2)
                    ->inlineLabel()
                    ->maxSize(204800) //  200 MB
                    ->acceptedFileTypes(['video/*'])
                    ->uploadProgressIndicatorPosition('left')
                    ->uploadButtonPosition('left')
                    ->removeUploadedFileButtonPosition('right')
                    ->preserveFilenames()
                    ->getUploadedFileNameForStorageUsing(
                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                            ->prepend('video-'),
                    )
                    ->afterStateUpdated(function (Get $get, $state, Set $set) {
                        if (! $state) {
                            return;
                        }

                        // For temporary uploaded files
                        if ($state instanceof TemporaryUploadedFile) {
                            $path = $state->getRealPath();
                        } else {
                            // For existing files
                            $path = storage_path('app/public/'.$state);
                        }

                        // Generate thumbnail or other metadata
                        // You could dispatch a job here as well
                        $duration = self::getVideoDuration($path);
                        $minutes = floor($duration / 60);
                        $seconds = $duration % 60;
                        $duration = gmdate('H:i:s', $duration);

                        $set('minutes', $minutes);
                        $set('seconds', $seconds);
//                        $set('duration', $duration);
                    }),

                // preview thumbnail https://customer-tara3gffqr183zil.cloudflarestream.com/3f90e217d452634a6208a51fdb520d5b/thumbnails/thumbnail.jpg
                Placeholder::make('preview')
                    ->hidden(fn($operation) => $operation === 'create')
                    ->content(function (Video $record) {
                        return new HtmlString('
                                    <a href="https://customer-'.config('services.cloudflare.code').'.cloudflarestream.com/'.$record->path.'/watch" target="_blank">
                                        <img src="https://customer-'.config('services.cloudflare.code').'.cloudflarestream.com/'.$record->path.'/thumbnails/thumbnail.jpg"  alt=""/>
                                    </a>
                                ');
                    }),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->sortable(),
                TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListVideos::route('/'),
            'create' => CreateVideo::route('/create'),
            'edit' => EditVideo::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }

    protected static function getVideoDuration($path): float
    {
        try {
            // Using FFprobe to get video duration
            $command = 'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 '.escapeshellarg($path);
            $duration = (float) shell_exec($command);

            return $duration ?: 0;
        } catch (Exception $e) {
            report($e);

            return 0;
        }
    }
}
