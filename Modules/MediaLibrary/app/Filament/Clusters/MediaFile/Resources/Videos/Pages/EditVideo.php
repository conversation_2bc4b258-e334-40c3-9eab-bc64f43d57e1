<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\Pages;

use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\VideoResource;

class EditVideo extends EditRecord
{
    protected static string $resource = VideoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
