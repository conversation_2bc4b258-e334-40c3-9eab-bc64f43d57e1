<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\Pages;

use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\VideoResource;

class ListVideos extends ListRecords
{
    protected static string $resource = VideoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
