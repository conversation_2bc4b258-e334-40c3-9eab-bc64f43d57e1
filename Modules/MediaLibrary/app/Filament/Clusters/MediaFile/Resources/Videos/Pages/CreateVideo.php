<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\Pages;

use App\Services\CloudflareStreamService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Storage;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\Videos\VideoResource;

class CreateVideo extends CreateRecord
{
    protected static string $resource = VideoResource::class;

    protected function getHeaderActions(): array
    {
        return [

        ];
    }

    /**
     * @throws ConnectionException
     */
    protected function afterCreate(): void
    {
        //        $response = uploadToCloudflareStream(Storage::path($this->record->path));
        //        $response = (new CloudflareStreamService())->performVod('https://customer-tara3gffqr183zil.cloudflarestream.com/3f90e217d452634a6208a51fdb520d5b/watch');
        $response = (new CloudflareStreamService)->performVod(url(Storage::url($this->record->path)), $this->record->name);
        /*
    "uid" => "3f90e217d452634a6208a51fdb520d5b"
    "creator" => null
    "thumbnail" => "https://customer-tara3gffqr183zil.cloudflarestream.com/3f90e217d452634a6208a51fdb520d5b/thumbnails/thumbnail.jpg"
  "thumbnailTimestampPct" => 0
  "readyToStream" => false
  "readyToStreamAt" => null
  "status" => array:3 [▶]
  "meta" => array:2 [▶]
  "created" => "2025-01-29T20:47:13.613977Z"
  "modified" => "2025-01-29T20:47:13.613977Z"
  "scheduledDeletion" => null
  "size" => 158008374
  "preview" => "https://customer-tara3gffqr183zil.cloudflarestream.com/3f90e217d452634a6208a51fdb520d5b/watch"
  "allowedOrigins" => []
  "requireSignedURLs" => false
  "uploaded" => "2025-01-29T20:47:13.613961Z"
  "uploadExpiry" => null
  "maxSizeBytes" => null
  "maxDurationSeconds" => null
  "duration" => -1
  "input" => array:2 [▶]
  "playback" => array:2 [▶]
  "watermark" => null
  "clippedFrom" => null
  "publicDetails" => null

         * */

        $this->record->update([
            'path' => $response['uid'],
            'meta' => [
                'cloudflare_stream_id' => $response['uid'],
                'cloudflare_stream_status' => $response['status'],
            ],
        ]);

        // after upload to cloudflare stream delete the file from local storage
        $filePath = $this->record->getOriginal('path'); // Get the original file path before it was updated
        if (Storage::disk('public')->exists($filePath)) {
            // Delete the file
            Storage::disk('public')->delete($filePath);
        }

    }
}
