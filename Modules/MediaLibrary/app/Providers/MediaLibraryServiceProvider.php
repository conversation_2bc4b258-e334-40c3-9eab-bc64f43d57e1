<?php

declare(strict_types=1);

namespace Modules\MediaLibrary\Providers;

use Modules\Core\Providers\BaseModuleServiceProvider;

final class MediaLibraryServiceProvider extends BaseModuleServiceProvider
{
    protected string $name = 'MediaLibrary';

    protected string $nameLower = 'medialibrary';

    /**
     * Register the service provider.
     */
    public function registerModule(): void
    {

    }

    /**
     * Get module commands.
     */
    protected function getModuleCommands(): array
    {
        return [];
    }
}
