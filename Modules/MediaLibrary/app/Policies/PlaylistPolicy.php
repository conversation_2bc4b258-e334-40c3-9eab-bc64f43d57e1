<?php

declare(strict_types=1);

namespace Modules\MediaLibrary\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\MediaLibrary\Models\Playlist;

final class PlaylistPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any playlist if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any playlists');
    }

    public function view(Admin $user, Playlist $playlist): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view playlists');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a playlist if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create playlists');
    }

    public function update(Admin $user, Playlist $playlist): bool
    {
        // Allow updating a playlist if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update playlists');
    }

    public function delete(Admin $user, Playlist $playlist): bool
    {
        // Allow deleting a playlist if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete playlists');
    }

    public function restore(Admin $user, Playlist $playlist): bool
    {
        // Allow restoring a playlist if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore playlists');
    }

    public function forceDelete(Admin $user, Playlist $playlist): bool
    {
        // Allow force deleting a playlist if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete playlists');
    }
}
