<?php

declare(strict_types=1);

namespace Modules\MediaLibrary\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\MediaLibrary\Models\Audio;

final class AudioPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any audio if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any audios');
    }

    public function view(Admin $user, Audio $audio): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view audios');
    }

    public function create(Admin $user): bool
    {
        // Allow creating an audio if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create audios');
    }

    public function update(Admin $user, Audio $audio): bool
    {
        // Allow updating an audio if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update audios');
    }

    public function delete(Admin $user, Audio $audio): bool
    {
        // Allow deleting an audio if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete audios');
    }

    public function restore(Admin $user, Audio $audio): bool
    {
        // Allow restoring an audio if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore audios');
    }

    public function forceDelete(Admin $user, Audio $audio): bool
    {
        // Allow force deleting an audio if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete audios');
    }
}
