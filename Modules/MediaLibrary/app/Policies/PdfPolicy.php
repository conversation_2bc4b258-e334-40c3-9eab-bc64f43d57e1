<?php

declare(strict_types=1);

namespace Modules\MediaLibrary\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\MediaLibrary\Models\Pdf;

final class PdfPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any pdf if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any pdfs');
    }

    public function view(Admin $user, Pdf $pdf): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view pdfs');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a pdf if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create pdfs');
    }

    public function update(Admin $user, Pdf $pdf): bool
    {
        // Allow updating a pdf if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update pdfs');
    }

    public function delete(Admin $user, Pdf $pdf): bool
    {
        // Allow deleting a pdf if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete pdfs');
    }

    public function restore(Admin $user, Pdf $pdf): bool
    {
        // Allow restoring a pdf if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore pdfs');
    }

    public function forceDelete(Admin $user, Pdf $pdf): bool
    {
        // Allow force deleting a pdf if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete pdfs');
    }
}
