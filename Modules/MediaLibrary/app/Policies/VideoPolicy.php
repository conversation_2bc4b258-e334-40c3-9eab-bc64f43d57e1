<?php

declare(strict_types=1);

namespace Modules\MediaLibrary\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\MediaLibrary\Models\Video;

final class VideoPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any video if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any videos');
    }

    public function view(Admin $user, Video $video): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view videos');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a video if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create videos');
    }

    public function update(Admin $user, Video $video): bool
    {
        // Allow updating a video if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update videos');
    }

    public function delete(Admin $user, Video $video): bool
    {
        // Allow deleting a video if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete videos');
    }

    public function restore(Admin $user, Video $video): bool
    {
        // Allow restoring a video if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore videos');
    }

    public function forceDelete(Admin $user, Video $video): bool
    {
        // Allow force deleting a video if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete videos');
    }
}
