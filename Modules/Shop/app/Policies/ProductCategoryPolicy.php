<?php

declare(strict_types=1);

namespace Modules\Shop\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Shop\Models\ProductCategory;

final class ProductCategoryPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any product category if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any product categories');
    }

    public function view(Admin $user, ProductCategory $productCategory): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view product categories');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a product category if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create product categories');
    }

    public function update(Admin $user, ProductCategory $productCategory): bool
    {
        // Allow updating a product category if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update product categories');
    }

    public function delete(Admin $user, ProductCategory $productCategory): bool
    {
        // Allow deleting a product category if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete product categories');
    }

    public function restore(Admin $user, ProductCategory $productCategory): bool
    {
        // Allow restoring a product category if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore product categories');
    }

    public function forceDelete(Admin $user, ProductCategory $productCategory): bool
    {
        // Allow force deleting a product category if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete product categories');
    }
}
