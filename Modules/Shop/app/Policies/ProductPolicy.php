<?php

declare(strict_types=1);

namespace Modules\Shop\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Shop\Models\Product;

final class ProductPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any product if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any products');
    }

    public function view(Admin $user, Product $product): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view products');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a product if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create products');
    }

    public function update(Admin $user, Product $product): bool
    {
        // Allow updating a product if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update products');
    }

    public function delete(Admin $user, Product $product): bool
    {
        // Allow deleting a product if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete products');
    }

    public function restore(Admin $user, Product $product): bool
    {
        // Allow restoring a product if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore products');
    }

    public function forceDelete(Admin $user, Product $product): bool
    {
        // Allow force deleting a product if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete products');
    }
}
