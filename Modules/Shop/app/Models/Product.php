<?php

namespace Modules\Shop\Models;

use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Billing\Models\Order;

class Product extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'sku',
        'images',
        'description',
        'additional',
        'quantity',
        'price',
        'is_visible',
        'is_featured',
        'type',
        'options',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'decimal:2',
        'options' => 'array',
        'images' => 'array',
        'additional' => 'array',
        'is_visible' => 'boolean',
        'is_featured' => 'boolean',
        'type' => 'string',
    ];

    public function product_categories(): BelongsToMany
    {
        return $this->belongsToMany(ProductCategory::class, 'product_category_product', 'product_id', 'product_category_id');
    }
    //
    //    public function getPriceAttribute($value)
    //    {
    //        return number_format($value, 2);
    //    }

    public function orders(): MorphToMany
    {
        return $this->morphToMany(Order::class, 'item', 'order_items')->with('items');
    }

    public function scopeSearched(Builder $query): void
    {
        $query->when(request('search'), function (Builder $q) {
            $q->whereFullText(['name', 'description'], request('search'));
        });
    }
}
