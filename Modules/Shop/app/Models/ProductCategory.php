<?php

namespace Modules\Shop\Models;

use Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductCategory extends Model
{
    protected $fillable = [
        'parent_id',
        'name',
        'slug',
        'is_visible',
        'description',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($tour) {
            $tour->slug = Str::slug($tour->title).'-'.random_int(1000, 9999);
        });
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_category_product', 'product_category_id', 'product_id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'parent_id');
    }

    public function child(): Has<PERSON>any
    {
        return $this->hasMany(ProductCategory::class, 'parent_id');
    }
}
