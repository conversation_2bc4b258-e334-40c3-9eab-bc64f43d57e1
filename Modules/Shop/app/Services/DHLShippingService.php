<?php

namespace Modules\Shop\Services;

use Exception;
use InvalidArgumentException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DHLShippingService
{
    private $client;
    private $apiKey;
    private $apiSecret;
    private $accountNumber;
    private $baseUrl;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiKey = config('services.dhl.api_key');
        $this->apiSecret = config('services.dhl.api_secret');
        $this->accountNumber = config('services.dhl.account_number');
        $this->baseUrl = config('services.dhl.api_url');
    }

    /**
     * Get OAuth access token with caching
     */
    private function getAccessToken(): string
    {
        $cacheKey = 'dhl_access_token';

        return Cache::remember($cacheKey, 3600, function () {
            try {
                $credentials = base64_encode($this->apiKey . ':' . $this->apiSecret);

                $response = $this->client->post($this->baseUrl . '/oauth2/token', [
                    'headers' => [
                        'Authorization' => 'Basic ' . $credentials,
                        'Content-Type' => 'application/x-www-form-urlencoded',
                    ],
                    'form_params' => [
                        'grant_type' => 'client_credentials',
                    ],
                ]);

                $data = json_decode($response->getBody()->getContents(), true);

                return $data['access_token'];
            } catch (RequestException $e) {
                Log::error('DHL Authentication failed: ' . $e->getMessage());
                throw new Exception('DHL authentication failed');
            }
        });
    }

    /**
     * Calculate shipping cost for an order
     */
    public function calculateShippingCost(array $orderData): array
    {
        try {
            $token = $this->getAccessToken();

            $rateRequest = $this->buildRateRequest($orderData);

            $response = $this->client->post($this->baseUrl . '/rates', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json',
                ],
                'json' => $rateRequest,
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return $this->parseRateResponse($data);
        } catch (RequestException $e) {
            $errorMessage = $e->getResponse()
                ? json_decode($e->getResponse()->getBody()->getContents(), true)
                : $e->getMessage();

            Log::error('DHL Rate calculation failed: ' . json_encode($errorMessage));
            throw new Exception('Unable to calculate shipping cost');
        }
    }

    /**
     * Build rate request payload
     */
    private function buildRateRequest(array $orderData): array
    {
        return [
            'customerDetails' => [
                'shipperDetails' => [
                    'postalAddress' => [
                        'postalCode' => $orderData['origin']['postal_code'],
                        'cityName' => $orderData['origin']['city'],
                        'countryCode' => $orderData['origin']['country_code'],
                        'addressLine1' => $orderData['origin']['address_line_1'],
                    ],
                ],
                'receiverDetails' => [
                    'postalAddress' => [
                        'postalCode' => $orderData['destination']['postal_code'],
                        'cityName' => $orderData['destination']['city'],
                        'countryCode' => $orderData['destination']['country_code'],
                        'addressLine1' => $orderData['destination']['address_line_1'],
                        'addressLine2' => $orderData['destination']['address_line_2'] ?? '',
                        'provinceCode' => $orderData['destination']['province_code'] ?? '',
                    ],
                ],
            ],
            'accounts' => [
                [
                    'typeCode' => 'shipper',
                    'number' => $this->accountNumber,
                ],
            ],
            'productCode' => $orderData['service_type'] ?? 'N',
            'localProductCode' => $orderData['service_type'] ?? 'N',
            'unitOfMeasurement' => 'metric',
            'isCustomsDeclarable' => $orderData['is_international'] ?? false,
            'packages' => array_map(function ($package) {
                return [
                    'typeCode' => '2BP',
                    'weight' => $package['weight'],
                    'dimensions' => [
                        'length' => $package['dimensions']['length'],
                        'width' => $package['dimensions']['width'],
                        'height' => $package['dimensions']['height'],
                    ],
                ];
            }, $orderData['packages']),
            'plannedShippingDateAndTime' => $orderData['ship_date'] ?? now()->toISOString(),
            'monetaryAmount' => isset($orderData['declared_value']) ? [
                [
                    'typeCode' => 'declared',
                    'value' => $orderData['declared_value']['amount'],
                    'currency' => $orderData['declared_value']['currency'],
                ]
            ] : [],
            'requestAllValueAddedServices' => false,
            'returnStandardProductsOnly' => false,
            'nextBusinessDay' => false,
        ];
    }

    /**
     * Parse DHL API response
     */
    private function parseRateResponse(array $response): array
    {
        if (!isset($response['products']) || empty($response['products'])) {
            throw new Exception('No shipping rates available for this destination');
        }

        return array_map(function ($product) {
            return [
                'service_type' => $product['productName'],
                'service_code' => $product['productCode'],
                'total_price' => [
                    'amount' => $product['totalPrice'][0]['price'] ?? 0,
                    'currency' => $product['totalPrice'][0]['priceCurrency'] ?? 'USD',
                ],
                'delivery_time' => [
                    'estimated_date' => $product['deliveryCapabilities']['estimatedDeliveryDateAndTime'] ?? null,
                    'delivery_type' => $product['deliveryCapabilities']['deliveryTypeCode'] ?? null,
                ],
                'breakdown' => array_map(function ($item) {
                    return [
                        'name' => $item['name'],
                        'price' => $item['price'],
                        'currency' => $item['priceCurrency'],
                    ];
                }, $product['breakdown'] ?? []),
            ];
        }, $response['products']);
    }

    /**
     * Validate address format
     */
    public function validateAddress(array $address): bool
    {
        $required = ['country_code', 'postal_code', 'city', 'address_line_1'];

        foreach ($required as $field) {
            if (empty($address[$field])) {
                throw new InvalidArgumentException("Missing required field: {$field}");
            }
        }

        if (!preg_match('/^[A-Z]{2}$/', $address['country_code'])) {
            throw new InvalidArgumentException('Country code must be 2-letter ISO format');
        }

        return true;
    }

    /**
     * Calculate shipping for Laravel Order model
     */
    public function calculateForOrder($order): array
    {
        // Prepare order data from your Order model
        $orderData = [
            'origin' => [
                'country_code' => 'US', // Your warehouse country
                'postal_code' => '10001', // Your warehouse postal code
                'city' => 'New York',
                'address_line_1' => '123 Warehouse Street',
            ],
            'destination' => [
                'country_code' => $order->shipping_country,
                'postal_code' => $order->shipping_postal_code,
                'city' => $order->shipping_city,
                'address_line_1' => $order->shipping_address_1,
                'address_line_2' => $order->shipping_address_2,
                'province_code' => $order->shipping_state,
            ],
            'packages' => [
                [
                    'weight' => $order->total_weight, // in kg
                    'dimensions' => [
                        'length' => $order->package_length, // in cm
                        'width' => $order->package_width,
                        'height' => $order->package_height,
                    ],
                ],
            ],
            'service_type' => 'N', // DHL Express 12:00
            'is_international' => $order->shipping_country !== 'US',
            'declared_value' => [
                'amount' => $order->total_amount,
                'currency' => 'USD',
            ],
        ];

        // Validate addresses
        $this->validateAddress($orderData['origin']);
        $this->validateAddress($orderData['destination']);

        return $this->calculateShippingCost($orderData);
    }
}
