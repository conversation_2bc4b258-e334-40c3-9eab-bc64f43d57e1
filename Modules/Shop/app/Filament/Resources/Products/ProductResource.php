<?php

namespace Modules\Shop\Filament\Resources\Products;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Shop\Filament\Resources\Products\Pages\ListProducts;
use Modules\Shop\Filament\Resources\Products\Pages\CreateProduct;
use Modules\Shop\Filament\Resources\Products\Pages\EditProduct;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Shop\Filament\Resources\ProductResource\Pages;
use Modules\Shop\Models\Product;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $slug = 'books';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $recordTitleAttribute = 'name';

    public static function getNavigationGroup(): ?string
    {
        return __('Learning');
    }

    public static function getLabel(): ?string
    {
        return __('Book');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Books');
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'slug', 'description'];
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columnSpanFull()
                    ->columns(3)
                    ->schema([
                        Group::make()
                            ->columnSpan(2)
                            ->schema([
                                Section::make()
                                    ->columnSpanFull()
                                    ->schema([
                                        TextInput::make('name')
                                            ->required()
                                            ->live(debounce: '750ms')
                                            ->afterStateUpdated(function (string $operation, $state, Set $set) {
                                                if ($operation !== 'create' && $operation !== 'createOption') {
                                                    return;
                                                }
                                                $set('slug', Str::slug($state));
                                            })
                                            ->afterStateHydrated(function (TextInput $component, $state) {
                                                $component->state(ucwords($state));
                                            }),

                                        TextInput::make('slug')
                                            ->disabled()
                                            ->dehydrated()
                                            ->required()
                                            ->unique(ignoreRecord: true),

                                        MarkdownEditor::make('description')
                                            ->columnSpan('full'),
                                    ])->columns(2),

                                Section::make('Pricing & Inventory')
                                    ->columnSpanFull()
                                    ->schema([
                                        TextInput::make('sku')
                                            ->unique(ignoreRecord: true)
                                            ->label('SKU (Stock Keeping Unit)')
                                            ->required(),

                                        TextInput::make('price')
                                            ->numeric()
                                            ->rules('regex:/^\d{1,6}(\.\d{0,2})?$/')
                                            ->required(),

                                        TextInput::make('quantity')
                                            ->numeric()
                                            ->minValue(0)
                                            ->maxValue(100)
                                            ->required(),

                                        Select::make('type')
                                            ->options([
                                                'downloadable' => 'Downloadable',
                                                'deliverable' => 'Deliverable',
                                            ])->default('deliverable')->required(),
                                    ])->columns(2),

                                Section::make('Additional Information')
                                    ->columnSpanFull()
                                    ->schema([
                                        KeyValue::make('additional')
                                            ->hiddenLabel()
                                            ->helperText('table for additional details for product like dimensions, weight, etc.')
                                            ->addActionLabel('Add detail')
                                            ->default(['Weight' => null, 'Size' => null])
                                            ->deletable(true)
                                            ->keyLabel('Detail Type')
                                            ->ValueLabel('Value'),
                                    ]),
                            ]),

                        Group::make()
                            ->columnSpan(1)
                            ->schema([
                                Section::make('Status')
                                    ->columnSpanFull()
                                    ->schema([
                                        Toggle::make('is_visible')
                                            ->label(__('Visibility'))
                                            ->helperText('Enable or disable product visibility')
                                            ->default(true),

                                        Toggle::make('is_featured')
                                            ->label(__('Featured'))
                                            ->helperText('Enable or disable products featured status'),

                                        //                                        Forms\Components\DatePicker::make('published_at')
                                        //                                            ->label(__('Availability'))
                                        //                                            ->default(now())
                                    ]),

                                //                                Forms\Components\Section::make('Image')
                                //                                    ->schema([
                                //                                        Forms\Components\FileUpload::make('image')
                                //                                            ->directory('form-attachments')
                                //                                            ->preserveFilenames()
                                //                                            ->required()
                                //                                            ->image()
                                //                                            ->imageEditor()
                                //                                    ])->collapsible(),

                                FileUpload::make('images')
                                    ->reorderable()
                                    ->imageEditor()
                                    ->multiple(),

                                Section::make('Associations')
                                    ->columnSpanFull()
                                    ->schema([
                                        Select::make('product_categories')
                                            ->label(__('Categories'))
                                            ->relationship('product_categories', 'name')
                                            ->createOptionForm([
                                                TextInput::make('name')
                                                    ->required()
                                                    ->maxLength(255),
                                            ])
                                            ->preload()
                                            ->multiple()
                                            ->required(),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('images.0')->circular(),

                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                IconColumn::make('is_visible')
                    ->sortable()
                    ->toggleable()
                    ->label(__('Visibility'))
                    ->boolean(),

                TextColumn::make('price')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('quantity')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('type'),

            ])
            ->filters([
                TernaryFilter::make('is_visible')
                    ->label(__('Visibility'))
                    ->boolean()
                    ->trueLabel('Only Visible Books')
                    ->falseLabel('Only Hidden Books')
                    ->native(false),

            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProducts::route('/'),
            'create' => CreateProduct::route('/create'),
            'edit' => EditProduct::route('/{record}/edit'),
        ];
    }
}
