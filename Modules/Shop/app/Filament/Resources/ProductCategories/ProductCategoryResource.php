<?php

namespace Modules\Shop\Filament\Resources\ProductCategories;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Shop\Filament\Resources\ProductCategories\Pages\ListProductCategories;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Shop\Filament\Resources\ProductCategoryResource\Pages;
use Modules\Shop\Models\Product;
use Modules\Shop\Models\ProductCategory;

class ProductCategoryResource extends Resource
{
    protected static ?string $model = ProductCategory::class;

    protected static ?string $slug = 'product-categories';

    protected static ?string $navigationParentItem = 'Books';

    protected static ?string $navigationLabel = 'Categories';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make()
                    ->columnSpanFull()
                    ->columns(3)
                    ->schema([

                        Group::make()
                            ->columnSpan(2)
                            ->schema([
                                Section::make()
                                    ->columnSpanFull()
                                    ->columns(2)
                                    ->schema([
                                        TextInput::make('name')
                                            ->required()
                                            ->live(debounce: '750ms')
                                            ->unique()
                                            ->afterStateUpdated(function (string $operation, $state, Set $set) {
                                                if ($operation !== 'create' && $operation !== 'createOption') {
                                                    return;
                                                }

                                                $set('slug', Str::slug($state));
                                            }),

                                        TextInput::make('slug')
                                            ->disabled()
                                            ->dehydrated()
                                            ->required()
                                            ->unique(Product::class, 'slug', ignoreRecord: true),

                                        MarkdownEditor::make('description')
                                            ->columnSpanFull(),
                                    ]),
                            ]),

                        Group::make()
                            ->columnSpan(1)
                            ->schema([
                                Section::make('Status')
                                    ->columnSpanFull()
                                    ->schema([
                                        Toggle::make('is_visible')
                                            ->label(__('Visibility'))
                                            ->helperText('Enable or disable category visibility')
                                            ->default(true),

                                        Select::make('parent_id')
                                            ->relationship('parent', 'name'),
                                    ]),
                            ]),
                    ]),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('parent.name')
                    ->label(__('Parent'))
                    ->searchable()
                    ->sortable(),

                IconColumn::make('is_visible')
                    ->label(__('Visibility'))
                    ->boolean()
                    ->sortable(),

                TextColumn::make('updated_at')
                    ->date()
                    ->label(__('Updated Date'))
                    ->sortable(),

            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProductCategories::route('/'),
            //            'create' => Pages\CreateProductCategory::route('/create'),
            //            'edit' => Pages\EditProductCategory::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
