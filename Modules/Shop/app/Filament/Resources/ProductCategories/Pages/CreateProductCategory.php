<?php

namespace Modules\Shop\Filament\Resources\ProductCategories\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Shop\Filament\Resources\ProductCategories\ProductCategoryResource;

class CreateProductCategory extends CreateRecord
{
    protected static string $resource = ProductCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [

        ];
    }
}
