<?php

namespace Modules\Shop\Filament\Resources\ProductCategories\Pages;

use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ManageRecords;
use Modules\Shop\Filament\Resources\ProductCategories\ProductCategoryResource;

class ListProductCategories extends ManageRecords
{
    protected static string $resource = ProductCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()->modalWidth('max-w-5xl'),
        ];
    }
}
