<?php

namespace Modules\Shop\Filament;

use Module;
use Filament\Contracts\Plugin;
use Filament\Panel;

class ShopPlugin implements Plugin
{
    public function getId(): string
    {
        return 'shop';
    }

    public function register(Panel $panel): void
    {
        if (Module::isEnabled('Shop')) {
            $panel->discoverResources(
                in: __DIR__.'/Resources',
                for: 'Modules\\Shop\\Filament\\Resources'
            );
        }
    }

    public function boot(Panel $panel): void {}
}
