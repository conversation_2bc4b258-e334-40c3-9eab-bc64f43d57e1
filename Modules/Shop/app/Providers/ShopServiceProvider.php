<?php

declare(strict_types=1);

namespace Modules\Shop\Providers;

use Cache;
use Modules\Core\Providers\BaseModuleServiceProvider;
use Modules\Shop\Models\Product;

final class ShopServiceProvider extends BaseModuleServiceProvider
{
    protected string $name = 'Shop';

    protected string $nameLower = 'shop';

    /**
     * Custom boot logic for Shop module.
     */
    public function bootModule(): void
    {
        if (!app()->runningInConsole()) {
            foreach (Cache::remember('all_products', 3600, fn () => Product::all()) as $product) {
                app('hook')->register('navItems', fn ($items) => $items + [route('product.show', ['product' => $product]) => $product->name . __('Product')]);
            }
        }
    }

    /**
     * Register the service provider.
     */
    public function registerModule(): void
    {

    }

    /**
     * Get module commands.
     */
    protected function getModuleCommands(): array
    {
        return [];
    }
}
