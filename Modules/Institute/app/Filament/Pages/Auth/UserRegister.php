<?php

namespace Modules\Institute\Filament\Pages\Auth;

use Filament\Auth\Pages\Register;
use Filament\Support\Enums\Width;
use Filament\Schemas\Components\Group;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use App\Models\User;
use Filament\Forms;
use Modules\Core\Models\Localization\City;
use Modules\Core\Models\Localization\Country;
use Modules\Institute\Concerns\UserTypes;
use Modules\Subscription\Services\SubscriptionService;

class UserRegister extends Register
{
    public ?string $type = null;

    public function beforeFill(): void
    {
        $this->type = request()->get('type');
    }

    //    protected static string $view = 'acl::filament.pages.auth.user-register';
    protected Width|string|null $maxWidth = '2xl';

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->components([

                        Group::make()
                            ->columns(1)
                            ->schema([
                                TextInput::make('username')

                                    ->required(),
                                $this->getNameFormComponent(),
                                $this->getEmailFormComponent(),
                                $this->getPasswordFormComponent(),
                                $this->getPasswordConfirmationFormComponent(),
                                TextInput::make('phone')
                                    ->required()

                                    ->tel()
                                    ->maxLength(255),
                            ]),
                        Group::make()
                            ->columns(2)
                            ->schema([
                                Select::make('country')
                                    ->reactive()
                                    ->dehydrated()
                                    ->columnSpanFull()
                                    ->options(Country::all()->pluck('name', 'name')->toArray())
                                    ->afterStateUpdated(fn (callable $set) => $set('parent_id', null))
                                    ->searchable(),

                                Select::make('gender')
                                    ->options(['male' => __('Male'), 'female' => __('Female')])
                                    ->columnSpan(2)
                                    ->required(),
                            ]),

                    ])->statePath('data')
            )];
    }

    protected function mutateFormDataBeforeRegister(array $data): array
    {
        $type = $this->type === 'institute' ? UserTypes::INSTITUTION->value : null;
        return [
            ...$data,
            'type' => $type,
        ];
    }

    public function afterRegister()
    {
        // check if affiliate module is enabled and if affiliate code exists in session
        if (! app()->has('affiliate')) {
            return;
        }

        if (session()->has('affiliate_code')) {
            if (! app('affiliate')->isValidAffiliateCode(session('affiliate_code'))) {
                session()->forget('affiliate_code');
            }else{
                session()->flash('affiliate_code', session('affiliate_code'));
            }
            app('affiliate')->trackReferral(
                session('affiliate_code'),
                $this->form->getModelInstance()->id
            );
        }

        // Assign free plan
        app('subscription')->assignFreePlan($this->form->getModelInstance());
    }
}
