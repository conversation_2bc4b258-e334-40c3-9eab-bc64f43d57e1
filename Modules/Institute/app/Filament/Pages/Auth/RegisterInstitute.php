<?php

namespace Modules\Institute\Filament\Pages\Auth;

use Filament\Support\Enums\Width;
use Filament\Schemas\Schema;
use Filament\Schemas\Components\Fieldset;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Tenancy\RegisterTenant;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Str;
use Modules\Core\Models\Localization\City;
use Modules\Core\Models\Localization\Country;
use Modules\Institute\Concerns\UserTypes;
use Modules\Institute\Models\Institute;

class RegisterInstitute extends RegisterTenant
{
    protected string $view = 'acl::filament.pages.auth.register-tenant';

    public static function getLabel(): string
    {
        return __('Register Institute');
    }

    protected function getFormStatePath(): ?string
    {
        return 'institute';
    }

    public static function registerNavigationItems(): array
    {
        return [];
    }

    protected Width|string|null $maxWidth = '6xl';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Fieldset::make()
                    ->schema([
                        TextInput::make('name')

                            ->placeholder(__('Enter a name'))
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Get $get, Set $set, string $operation, ?string $old, ?string $state) {
                                $set('slug', Str::slug($state));
                            })
                            ->afterStateHydrated(function (TextInput $component, $state) {
                                $component->state(ucwords($state));
                            })
                            ->required()
                            ->maxLength(255)
                            ->autofocus(),

                        Hidden::make('slug')

                            ->alphaDash()
                            ->required()
                            ->unique(ignoreRecord: true),

                        TextInput::make('email')

                            ->placeholder(__('Enter an email'))
                            ->email()
                            ->required()
                            ->maxLength(255),

                        TextInput::make('phone')

                            ->placeholder(__('Enter a phone'))
                            ->tel(),
                        TextInput::make('web')

                            ->placeholder(__('Enter a website of the institute'))
                            ->url(),

                    ]),

                Fieldset::make()
                    ->schema([
                        TextInput::make('address1')
                            ->inlineLabel()

                            ->placeholder(__('Enter an address 1')),

                        TextInput::make('address2')
                            ->inlineLabel()

                            ->hint('optional')
                            ->placeholder(__('Enter an address 2')),

                        Select::make('country_id')

                            ->inlineLabel()
                            ->reactive()
                            ->dehydrated()
                            ->options(Country::all()->pluck('name', 'id')->toArray())
                            ->afterStateUpdated(fn (callable $set) => $set('parent_id', null))
                            ->searchable(),

                        Select::make('state_id')

                            ->inlineLabel()
                            ->reactive()
                            ->dehydrated()
                            ->options(fn (callable $get) => City::whereCountryId($get('country_id'))?->pluck('name', 'id')->toArray())
                            ->disabled(fn (callable $get) => ! $get('country_id')),

                        Select::make('city_id')

                            ->inlineLabel()
                            ->reactive()
                            ->options(fn (callable $get) => City::whereStateId($get('state_id'))?->pluck('name', 'id')->toArray())
                            ->disabled(fn (callable $get) => ! $get('state_id')),
                    ]),

                FileUpload::make('photo')
                    ->columnSpanFull()

                    ->placeholder(__('Upload a photo')),

            ]);
    }

    protected function mutateFormDataBeforeRegister(array $data): array
    {
        return [
            ...$data,
            'user_id' => auth()->id(),
        ];
    }

    protected function handleRegistration(array $data): Institute
    {
        //        Gate::forUser(auth()->user())->authorize('create', Institute::class);

        $institute = Institute::create($data);

        $institute->members()->attach(auth()->user());

        return $institute;
    }

    public static function canView(): bool
    {
        try {
            return \auth()->user()->institute()->count() == 0 && \auth()->user()->type->value == UserTypes::INSTITUTION->value;
        } catch (AuthorizationException $exception) {
            return $exception->toResponse()->allowed();
        }
    }
}
