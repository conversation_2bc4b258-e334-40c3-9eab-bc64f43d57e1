<?php

namespace Modules\Institute\Filament\Resources\Institutes;

use Filament\Schemas\Schema;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Fieldset;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Modules\Institute\Filament\Resources\Institutes\Pages\ListInstitutes;
use Modules\Institute\Filament\Resources\Institutes\Pages\CreateInstitute;
use Modules\Institute\Filament\Resources\Institutes\Pages\EditInstitute;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Core\Models\Localization\Area;
use Modules\Core\Models\Localization\City;
use Modules\Core\Models\Localization\Country;
use Modules\Institute\Filament\Resources\InstituteResource\Pages;
use Modules\Institute\Models\Institute;

class InstituteResource extends Resource
{
    protected static ?string $model = Institute::class;

    protected static ?string $slug = 'institutes';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getPluralLabel(): ?string
    {
        return __('Institutes');
    }

    public static function getLabel(): ?string
    {
        return __('Institute');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Access');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema->components([
            Section::make()
                ->columnSpanFull()
                ->schema([
                Fieldset::make()
                    ->columnSpanFull()
                    ->schema([
                    TextInput::make('name')
                        ->inlineLabel()
                        ->label(__('Name'))
                        ->placeholder(__('Enter a name'))
                        ->live(onBlur: true)
                        ->afterStateUpdated(function (
                            Get $get,
                            Set $set,
                            string $operation,
                            ?string $old,
                            ?string $state
                        ) {
                            if (
                                ($get('slug') ?? '') !== Str::slug($old) ||
                                $operation !== 'create'
                            ) {
                                return;
                            }
                            $set('slug', Str::slug($state));
                        })
                        ->afterStateHydrated(function (
                            TextInput $component,
                            $state
                        ) {
                            $component->state(ucwords($state));
                        })
                        ->required()
                        ->maxLength(255)
                        ->autofocus(),

                    Hidden::make('slug')
                        ->inlineLabel()
                        ->label(__('Slug'))
                        ->alphaDash()
                        ->required()
                        ->unique(ignoreRecord: true),

                    TextInput::make('email')
                        ->inlineLabel()
                        ->label(__('Email'))
                        ->placeholder(__('Enter an email'))
                        ->email()
                        ->required()
                        ->maxLength(255),

                    Select::make('user_id')
                        ->hidden(fn ($operation): bool => $operation === 'edit')
                        ->inlineLabel()
                        ->label(__('Owner'))
                        ->preload()
                        ->searchable()
                        ->relationship('user', 'name'),

                    TextInput::make('phone')
                        ->inlineLabel()
                        ->label(__('Phone'))
                        ->placeholder(__('Enter a phone'))
                        ->tel(),
                    TextInput::make('web')
                        ->inlineLabel()
                        ->label(__('Web'))
                        ->placeholder(__('Enter a website of the institute'))
                        ->url(),
                ]),

                Fieldset::make()
                    ->columnSpanFull()
                    ->schema([
                    TextInput::make('address1')
                        ->inlineLabel()
                        ->label(__('Address 1'))
                        ->placeholder(__('Enter an address 1')),

                    TextInput::make('address2')
                        ->inlineLabel()
                        ->label(__('Address 2'))
                        ->hint('optional')
                        ->placeholder(__('Enter an address 2')),

                    Select::make('country_id')
                        ->label(__('Country'))
                        ->inlineLabel()
                        ->live()
                        ->dehydrated()
                        ->options(
                            Country::all()->pluck('name', 'id')->toArray()
                        )
                        ->afterStateUpdated(
                            fn (callable $set) => $set('parent_id', null)
                        )
                        ->searchable(),

                    Select::make('state_id')
                        ->label(__('State'))
                        ->inlineLabel()
                        ->live()
                        ->dehydrated()
                        ->options(
                            fn (callable $get) => City::whereCountryId(
                                $get('country_id')
                            )
                                ?->pluck('name', 'id')
                                ->toArray()
                        )
                        ->disabled(fn (callable $get) => ! $get('country_id')),

                    Select::make('city_id')
                        ->label(__('City'))
                        ->inlineLabel()
                        ->live()
                        ->options(
                            fn (callable $get) => Area::whereCityId(
                                $get('state_id')
                            )
                                ?->pluck('name', 'id')
                                ->toArray()
                        )
                        ->disabled(fn (callable $get) => ! $get('state_id')),
                ]),
            ]),

            Section::make()
                ->columnSpanFull()
                ->schema([
                FileUpload::make('photo')
                    ->columnSpanFull()
                    ->label(__('Upload Cover Photo'))
                    ->placeholder(__('Upload a photo')),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable()->sortable(),
                TextColumn::make('user.name')->label('Owner')->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([EditAction::make(), DeleteAction::make()])
            ->toolbarActions([BulkActionGroup::make([DeleteBulkAction::make()])]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListInstitutes::route('/'),
            'create' => CreateInstitute::route('/create'),
            'edit' => EditInstitute::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name'];
    }
}
