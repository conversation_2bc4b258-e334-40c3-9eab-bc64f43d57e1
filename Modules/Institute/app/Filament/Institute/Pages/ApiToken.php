<?php

namespace Modules\Institute\Filament\Institute\Pages;

use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\CheckboxList;
use Filament\Actions\BulkAction;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Pages\Page;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class ApiToken extends Page implements HasTable
{
    use InteractsWithTable;

    public ?string $token = null;

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-finger-print';

    protected string $view = 'institute::filament.institute.pages.api-token';

    protected function getTableQuery(): Builder
    {
        session()->flash('sanctum-token', $this->token);

        return Auth::user()->tokens()->getQuery();
    }

    protected function getTableColumns(): array
    {
        return [
            TextColumn::make('name')
                ->label(trans('Name'))
                ->sortable()
                ->searchable(),
            TextColumn::make('abilities')->badge()
                ->label(trans('Abilities')),
            TextColumn::make('last_used_at')
                ->label(trans('Last used at'))
                ->dateTime()
                ->sortable(),
            TextColumn::make('created_at')
                ->label(trans('Created at'))
                ->dateTime()
                ->sortable(),
        ];
    }

    protected function getActions(): array
    {
        return [
            Action::make('new')
                ->label(trans('Create a new Token'))
                ->action(function (array $data) {
                    $user = Auth::user();
                    $token = $user->createToken($data['name'], $data['abilities'])->plainTextToken;
                    $this->token = $token;
                    session()->flash('sanctum-token', $token);
                })
                ->schema([
                    TextInput::make('name')
                        ->label(trans('Token Name'))
                        ->required(),
                    CheckboxList::make('abilities')
                        ->label(trans('Abilities'))
                        ->options([
                            'test' => 'Test',
                        ])
                        ->columns(4),
                ]),
        ];
    }

    protected function getTableBulkActions(): array
    {
        return [
            BulkAction::make('revoke')
                ->label(trans('Revoke'))
                ->action(fn (Collection $records) => $records->each->delete())
                ->deselectRecordsAfterCompletion()
                ->requiresConfirmation()
                ->color('danger')
                ->icon('heroicon-o-trash'),
        ];
    }
}
