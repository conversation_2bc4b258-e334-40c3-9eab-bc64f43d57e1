<?php

declare(strict_types=1);

namespace Modules\Institute\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Institute\Models\ClassRoom;

final class ClassRoomPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any classroom if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any classrooms');
    }

    public function view(Admin $user, ClassRoom $classRoom): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view classrooms');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a classroom if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create classrooms');
    }

    public function update(Admin $user, ClassRoom $classRoom): bool
    {
        // Allow updating a classroom if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update classrooms');
    }

    public function delete(Admin $user, ClassRoom $classRoom): bool
    {
        // Allow deleting a classroom if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete classrooms');
    }

    public function restore(Admin $user, ClassRoom $classRoom): bool
    {
        // Allow restoring a classroom if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore classrooms');
    }

    public function forceDelete(Admin $user, ClassRoom $classRoom): bool
    {
        // Allow force deleting a classroom if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete classrooms');
    }
}
