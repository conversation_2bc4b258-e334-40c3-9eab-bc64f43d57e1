<?php

declare(strict_types=1);

namespace Modules\Institute\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Institute\Models\Institute;

final class InstitutePolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any institute if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any institutes');
    }

    public function view(Admin $user, Institute $institute): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view institutes');
    }

    public function create(Admin $user): bool
    {
        // Allow creating an institute if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create institutes');
    }

    public function update(Admin $user, Institute $institute): bool
    {
        // Allow updating an institute if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update institutes');
    }

    public function delete(Admin $user, Institute $institute): bool
    {
        // Allow deleting an institute if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete institutes');
    }

    public function restore(Admin $user, Institute $institute): bool
    {
        // Allow restoring an institute if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore institutes');
    }

    public function forceDelete(Admin $user, Institute $institute): bool
    {
        // Allow force deleting an institute if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete institutes');
    }
}
