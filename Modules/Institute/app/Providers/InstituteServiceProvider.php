<?php

declare(strict_types=1);

namespace Modules\Institute\Providers;

use Modules\Core\Providers\BaseModuleServiceProvider;

final class InstituteServiceProvider extends BaseModuleServiceProvider
{
    protected string $name = 'Institute';

    protected string $nameLower = 'institute';

    /**
     * Register the service provider.
     */
    public function registerModule(): void
    {

    }

    /**
     * Get module commands.
     */
    protected function getModuleCommands(): array
    {
        return [];
    }
}
