<?php

namespace Modules\Institute\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\belongsToMany;
use Illuminate\Database\Eloquent\Relations\hasMany;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class Institute extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'city_id',
        'phone',
        'email',
        'address2',
        'address1',
        'default_lang',
        'web',
        'photo',
        'user_id',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array',
    ];

    // Owner user
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function members(): belongsToMany
    {
        return $this->belongsToMany(User::class, 'institute_user');
    }

    public function classRooms(): hasMany
    {
        return $this->hasMany(ClassRoom::class);
    }

    public function institute_invitations(): hasMany
    {
        return $this->hasMany(InstituteInvitation::class);
    }
}
