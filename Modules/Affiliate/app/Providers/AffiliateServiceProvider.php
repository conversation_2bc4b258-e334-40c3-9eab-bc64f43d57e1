<?php

declare(strict_types=1);

namespace Modules\Affiliate\Providers;

use Modules\Core\Providers\BaseModuleServiceProvider;
use Modules\Affiliate\Services\AffiliateService;
use Modules\Affiliate\Services\CouponService;

final class AffiliateServiceProvider extends BaseModuleServiceProvider
{
    protected string $name = 'Affiliate';

    protected string $nameLower = 'affiliate';

    /**
     * Register the service provider.
     */
    public function registerModule(): void
    {
        $this->app->bind('affiliate', AffiliateService::class);
        $this->app->bind('coupon', CouponService::class);
    }

    /**
     * Get module commands.
     */
    protected function getModuleCommands(): array
    {
        return [];
    }
}
