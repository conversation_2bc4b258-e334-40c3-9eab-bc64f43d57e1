<?php

namespace Modules\Affiliate\Filament\Resources\Coupons\RelationManagers;

use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class OrdersRelationManager extends RelationManager
{
    protected static string $relationship = 'orders';

    protected static ?string $recordTitleAttribute = 'order_number';

    protected static ?string $title = 'Orders Using This Coupon';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('order_number')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('customer.name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('total_amount')
                    ->money('usd')
                    ->sortable(),

                TextColumn::make('discount_amount')
                    ->money('usd')
                    ->sortable(),

                TextColumn::make('final_amount')
                    ->money('usd')
                    ->sortable()

                    ->getStateUsing(function ($record) {
                        return $record->total_amount - $record->discount_amount;
                    }),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'secondary',
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),

                SelectFilter::make('created_at')

                    ->options([
                        'today' => 'Today',
                        'week' => 'Last 7 Days',
                        'month' => 'Last 30 Days',
                        'year' => 'This Year',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when($data['value'], function (Builder $query, $value) {
                            return match ($value) {
                                'today' => $query->whereDate('created_at', today()),
                                'week' => $query->whereDate('created_at', '>=', now()->subDays(7)),
                                'month' => $query->whereDate('created_at', '>=', now()->subDays(30)),
                                'year' => $query->whereYear('created_at', now()->year),
                            };
                        });
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->toolbarActions([])  // Disabled bulk actions for safety
            ->poll('60s')      // Auto-refresh every 60 seconds
            ->striped()
            ->headerActions([
                // You can add custom actions here if needed
            ])
            ->recordActions([
                ViewAction::make()
                    ->url(fn ($record) => route('filament.resources.orders.view', $record)),
            ]);
    }
}
