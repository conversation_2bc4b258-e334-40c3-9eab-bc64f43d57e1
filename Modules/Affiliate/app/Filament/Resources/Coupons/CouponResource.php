<?php

namespace Modules\Affiliate\Filament\Resources\Coupons;

use Filament\Schemas\Schema;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Actions\EditAction;
use Filament\Actions\DeleteAction;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Affiliate\Filament\Resources\Coupons\Pages\CreateCoupon;
use Modules\Affiliate\Filament\Resources\Coupons\Pages\EditCoupon;
use Modules\Affiliate\Filament\Resources\Coupons\Pages\ListCoupons;
use Modules\Affiliate\Filament\Resources\Coupons\RelationManagers\OrdersRelationManager;
use Modules\Affiliate\Models\Coupon;

class CouponResource extends Resource
{
    protected static ?string $model = Coupon::class;

    protected static ?string $slug = 'coupons';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getLabel(): ?string
    {
        return __('Coupon');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Marketing');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Coupons');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema->components([
            TextInput::make('code')
                ->required()
                ->unique(ignoreRecord: true),
            Select::make('type')
                ->options([
                    'percentage' => 'Percentage',
                    'fixed' => 'Fixed Amount',
                ])
                ->required(),
            TextInput::make('value')
                ->numeric()
                ->required(),
            TextInput::make('min_order_value')
                ->numeric()
                ->required(),
            TextInput::make('max_discount')
                ->numeric()
                ->nullable(),
            DateTimePicker::make('starts_at')
                ->required(),
            DateTimePicker::make('expires_at')
                ->required(),
            TextInput::make('max_uses')
                ->numeric()
                ->required(),
            Toggle::make('is_active')
                ->default(true),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code'),
                TextColumn::make('type'),
                TextColumn::make('value'),
                TextColumn::make('used_count'),
                TextColumn::make('expires_at'),
                IconColumn::make('is_active')
                    ->boolean(),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'percentage' => 'Percentage',
                        'fixed' => 'Fixed Amount',
                    ]),
                TernaryFilter::make('is_active'),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            OrdersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCoupons::route('/'),
            'create' => CreateCoupon::route('/create'),
            'edit' => EditCoupon::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
