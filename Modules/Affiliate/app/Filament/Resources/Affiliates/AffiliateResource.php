<?php

namespace Modules\Affiliate\Filament\Resources\Affiliates;

use Filament\Schemas\Schema;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Actions\EditAction;
use Filament\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Modules\Affiliate\Filament\Resources\Affiliates\Pages\ListAffiliates;
use Modules\Affiliate\Filament\Resources\Affiliates\Pages\CreateAffiliate;
use Modules\Affiliate\Filament\Resources\Affiliates\Pages\EditAffiliate;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Affiliate\Filament\Resources\AffiliateResource\Pages;
use Modules\Affiliate\Filament\Resources\Affiliates\RelationManagers\AffiliateCouponsRelationManager;
use Modules\Affiliate\Filament\Resources\Affiliates\RelationManagers\ReferralsRelationManager;
use Modules\Affiliate\Models\Affiliate;
use Modules\Affiliate\Services\CouponService;

class AffiliateResource extends Resource
{
    protected static ?string $model = Affiliate::class;

    protected static ?string $slug = 'affiliates';

    protected static string | \BackedEnum | null $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Marketing');
    }

    public static function getLabel(): ?string
    {
        return __('Affiliate');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Affiliates');
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('commission_rate')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(100),
                TextInput::make('code')
                    ->required()
                    ->unique(ignoreRecord: true),
                TextInput::make('total_earnings')
                    ->disabled()
                    ->dehydrated(false),
                TextInput::make('total_referrals')
                    ->disabled()
                    ->dehydrated(false),
                Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->required(),
                Toggle::make('is_active')
                    ->required(),

                Toggle::make('is_active')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('code')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('commission_rate')
                    ->sortable(),
                TextColumn::make('total_earnings')
                    ->money('usd')
                    ->sortable(),
                TextColumn::make('total_referrals')
                    ->sortable(),
                BooleanColumn::make('is_active')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('is_active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ]),
            ])
            ->recordActions([
                EditAction::make(),
                Action::make('createCoupon')

                    ->icon('heroicon-o-ticket')
                    ->color('success')
                    ->schema([
                        Select::make('type')

                            ->options([
                                'percentage' => 'Percentage',
                                'fixed' => 'Fixed Amount',
                            ])
                            ->required(),
                        TextInput::make('value')

                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                if ($get('type') === 'percentage' && $state > 100) {
                                    $set('value', 100);
                                }
                            }),
                        TextInput::make('max_uses')

                            ->numeric()
                            ->minValue(1)
                            ->nullable(),
                        TextInput::make('min_order_value')
                            ->numeric()
                            ->required(),
                        DateTimePicker::make('expires_at')

                            ->nullable(),
                    ])
                    ->action(function (Affiliate $record, array $data): void {
                        $couponService = app(CouponService::class);

                        $couponService->createCoupon(
                            affiliateId: $record->id,
                            type: $data['type'],
                            value: $data['value'],
                            min_order_value: $data['min_order_value'],
                            usageLimit: $data['max_uses'] ?? null,
                            expiresAt: $data['expires_at'] ?? null
                        );

                        Notification::make()
                            ->title('Coupon created successfully')
                            ->success()
                            ->send();
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ReferralsRelationManager::class,
            AffiliateCouponsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAffiliates::route('/'),
            'create' => CreateAffiliate::route('/create'),
            'edit' => EditAffiliate::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
