<?php

declare(strict_types=1);

namespace Modules\Affiliate\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Affiliate\Models\Affiliate;

final class AffiliatePolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any affiliate if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any affiliates');
    }

    public function view(Admin $user, Affiliate $affiliate): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view affiliates');
    }

    public function create(Admin $user): bool
    {
        // Allow creating an affiliate if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create affiliates');
    }

    public function update(Admin $user, Affiliate $affiliate): bool
    {
        // Allow updating an affiliate if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update affiliates');
    }

    public function delete(Admin $user, Affiliate $affiliate): bool
    {
        // Allow deleting an affiliate if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete affiliates');
    }

    public function restore(Admin $user, Affiliate $affiliate): bool
    {
        // Allow restoring an affiliate if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore affiliates');
    }

    public function forceDelete(Admin $user, Affiliate $affiliate): bool
    {
        // Allow force deleting an affiliate if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete affiliates');
    }
}
