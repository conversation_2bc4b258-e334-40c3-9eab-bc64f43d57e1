<?php

declare(strict_types=1);

namespace Modules\Affiliate\Policies;

use Modules\Core\Models\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Affiliate\Models\Coupon;

final class CouponPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $user): bool
    {
        // Allow viewing any coupon if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('view any coupons');
    }

    public function view(Admin $user, Coupon $coupon): bool
    {
        return $user->hasRole('admin') || $user->checkPermissionTo('view coupons');
    }

    public function create(Admin $user): bool
    {
        // Allow creating a coupon if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('create coupons');
    }

    public function update(Admin $user, Coupon $coupon): bool
    {
        // Allow updating a coupon if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('update coupons');
    }

    public function delete(Admin $user, Coupon $coupon): bool
    {
        // Allow deleting a coupon if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('delete coupons');
    }

    public function restore(Admin $user, Coupon $coupon): bool
    {
        // Allow restoring a coupon if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('restore coupons');
    }

    public function forceDelete(Admin $user, Coupon $coupon): bool
    {
        // Allow force deleting a coupon if the user is an admin or has the appropriate role
        return $user->hasRole('admin') || $user->checkPermissionTo('force delete coupons');
    }
}
