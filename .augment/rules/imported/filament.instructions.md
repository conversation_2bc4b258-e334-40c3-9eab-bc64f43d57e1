---
type: "manual"
---

# FilamentPHP v4 Documentation & Instructions

## Filament Core

**Filament** is a Server-Driven UI (SDUI) framework for Laravel. It allows developers to define user interfaces in PHP using structured configuration objects. It is built on top of Livewire, Alpine.js, and Tailwind CSS.

- You can use the `search-docs` tool to get information from the official Filament documentation when needed. This is very useful for Artisan command arguments, specific code examples, testing functionality, relationship management, and ensuring you're following idiomatic practices.
- Utilize static `make()` methods for consistent component initialization.

### Artisan Commands
- You must use the Filament specific Artisan commands to create new files or components for Filament. You can find these with the `list-artisan-commands` tool, or with `php artisan` and the `--help` option.
- Inspect the required options, always pass `--no-interaction`, and valid arguments for other options when applicable.

### Filament's Core Features
- **Actions**: Handle user interactions with encapsulated UI, modals, and logic. Used for one-time operations like deleting records, sending emails, or updating data. All actions extend `Filament\Actions\Action` in v4.
- **Forms**: Dynamic forms with schema-driven components for data input and validation.
- **Infolists**: Read-only display of structured data with customizable entries.
- **Notifications**: Flash notifications for user feedback within the application.
- **Panels**: Top-level containers that organize pages, resources, forms, tables, and widgets.
- **Resources**: Static classes for CRUD interfaces on Eloquent models. Live in `app/Filament/Resources/`.
- **Schemas**: Define UI structure and behavior using components from `Filament\Schemas\Components`.
- **Tables**: Interactive data tables with filtering, sorting, pagination, and bulk actions.
- **Widgets**: Dashboard components for displaying charts, stats, and summary data.

### Schema Component Architecture (v4)
- **Layout Components**: `Grid`, `Section`, `Fieldset`, `Tabs`, `Wizard` from `Filament\Schemas\Components`
- **Prime Components**: `Text`, `Icon`, `Image`, `UnorderedList` for arbitrary content
- **Custom Components**: Create with `php artisan make:filament-schema-component ComponentName`
- **View Components**: Embed Blade templates with `View::make('path.to.view')`
- **Livewire Components**: Nest interactive components with `Livewire::make(ComponentClass::class)`

### Modern Icon Usage (v4)
```php
use Filament\Support\Icons\Heroicon;
use Filament\Schemas\Components\Icon;

Icon::make(Heroicon::InformationCircle)
```

### Relationship Handling
```php
// Prefer relationship() method for form components
Forms\Components\Select::make('user_id')
    ->label('Author')
    ->relationship('author', 'name')
    ->required()
    ->searchable()
```

---

## Filament v4 Breaking Changes

### Critical Breaking Changes
- **File visibility is now `private` by default** for FileUpload, ImageColumn, and ImageEntry components.
- **Table filters are deferred by default** - users must click a button before filters apply. Use `deferFilters(false)` to disable.
- **Layout components no longer span full width** - `Grid`, `Section`, and `Fieldset` only consume one column by default. Use `columnSpanFull()` to span all columns.
- **Pagination 'all' option removed** - no longer available by default. Add manually with `paginationPageOptions([5, 10, 25, 50, 'all'])`.
- **All actions now extend `Filament\Actions\Action`** - no separate `Filament\Tables\Actions` classes exist.
- **Layout components moved to `Filament\Schemas\Components`** - includes `Grid`, `Section`, `Fieldset`, `Tabs`, `Wizard`, etc.
- **Unique validation behavior changed** - `unique()` method now ignores current record by default (`ignoreRecord: true`).
- **Icons use `Filament\Support\Icons\Heroicon` Enum** - consistent icon usage across components.

### Schemas Architecture (New in v4)
- **Schemas** define the structure and behavior of UI components (forms, tables, infolists).
- **Prime Components** for arbitrary content: `Text`, `Icon`, `Image`, `UnorderedList`.
- **Schema Components** live in `Filament\Schemas\Components` namespace.
- **Custom Schema Components** can be created with `php artisan make:filament-schema-component ComponentName`.

### New Directory Structure (v4 Default)
- **Resources by domain**: `app/Filament/Resources/Users/<USER>/Filament/Resources/UserResource/`
- **Clusters organized**: `app/Filament/Clusters/Settings/` for grouping related resources
- **Schema separation**: Forms and tables can be in separate schema classes
- **Component organization**:
  - Schema components: `Schemas/Components/`
  - Table columns: `Tables/Columns/`
  - Table filters: `Tables/Filters/`
  - Actions: `Actions/`

### File Generation Flags (v4 Configuration)
```php
use Filament\Support\Commands\FileGenerators\FileGenerationFlag;

'file_generation' => [
    'flags' => [
        FileGenerationFlag::EMBEDDED_PANEL_RESOURCE_SCHEMAS, // Forms/infolists inside resource class
        FileGenerationFlag::EMBEDDED_PANEL_RESOURCE_TABLES, // Tables inside resource class  
        FileGenerationFlag::PARTIAL_IMPORTS, // Partial component imports
    ],
],
```

### Schema Components Best Practices
- Use **prime components** for arbitrary content in schemas
- **Text component** for styled text with weight, color, size options
- **Icon component** with `Heroicon` enum for consistent iconography
- **Image component** for displaying images with sizing and alignment
- **View component** for custom Blade templates within schemas
- **Livewire component** for nested interactive components (limited capabilities)

### Tailwind CSS v4 Compatibility
- **Custom themes must use Tailwind v4** with `@source` directives instead of `@config`
- **No tailwind.config.js** - configuration now in CSS with `@source` paths
- **Run upgrade tool**: `npx @tailwindcss/upgrade` for automatic migration

### Component Utility Injection
- **All schema components support utility injection** in configuration methods
- **Use parameter names** like `Get $get`, `Set $set`, `Record $record` for injection
- **Static vs dynamic values** - methods accept both static values and functions

---

## Filament v4 Best Practices

### Component Organization
- **Keep resources lean** by extracting complex components into dedicated classes
- **Use schema classes** for complex form/table definitions
- **Organize by feature** rather than by technical layer
- **Create reusable components** for repeated patterns

### Schema Component Usage
```php
// Use prime components for content
use Filament\Schemas\Components\Text;
use Filament\Support\Enums\FontWeight;

Text::make('Welcome to the dashboard')
    ->weight(FontWeight::Bold)
    ->color('primary')
```

### Modern Layouts with v4
```php
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;

Section::make('User Information')
    ->schema([
        Grid::make(2)
            ->schema([
                TextInput::make('first_name'),
                TextInput::make('last_name'),
            ]),
    ])
    ->columnSpanFull() // Required in v4 for full width
```

### File Visibility Configuration
```php
// Configure globally in AppServiceProvider
use Filament\Forms\Components\FileUpload;

FileUpload::configureUsing(fn (FileUpload $upload) => $upload
    ->visibility('public')); // Override v4 default if needed
```

### Action Definition (v4)
```php
use Filament\Actions\Action;
use Filament\Support\Icons\Heroicon;

Action::make('export')
    ->label('Export Data')
    ->icon(Heroicon::DocumentArrowDown)
    ->action(fn () => $this->export())
```

### Custom Schema Components
```php
// Create with artisan command
// php artisan make:filament-schema-component CustomCard

namespace App\Filament\Schemas\Components;

use Filament\Schemas\Components\Component;

class CustomCard extends Component
{
    protected string $view = 'filament.schemas.components.custom-card';
    
    public static function make(): static
    {
        return app(static::class);
    }
    
    public function title(string $title): static
    {
        $this->title = $title;
        return $this;
    }
}
```

### Upgrade Compatibility Tips
- **Use `columnSpanFull()`** for layout components that should span full width
- **Check file visibility** settings for FileUpload components
- **Update icon imports** to use `Filament\Support\Icons\Heroicon`
- **Review table filter behavior** - now deferred by default
- **Test unique validation** - now ignores current record by default

---

## Testing Filament v4

### Testing Guidelines
- **Test Filament v4 functionality** thoroughly for user satisfaction and reliability.
- **Authenticate users** properly within tests to access protected panels and resources.
- **Use Livewire testing methods** - start assertions with `livewire()` or `Livewire::test()`.
- **Test schema components** using `assertSchemaComponentExists()` with component keys.

### Example Tests

#### Filament v4 Table Test
```php
livewire(ListUsers::class)
    ->assertCanSeeTableRecords($users)
    ->searchTable($users->first()->name)
    ->assertCanSeeTableRecords($users->take(1))
    ->assertCanNotSeeTableRecords($users->skip(1))
    ->assertDeferredTableFilters(); // v4 specific
```

#### Filament v4 Resource Creation Test
```php
livewire(CreateUser::class)
    ->fillForm([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
    ])
    ->call('create')
    ->assertNotified()
    ->assertRedirect();

assertDatabaseHas(User::class, [
    'name' => 'John Doe',
    'email' => '<EMAIL>',
]);
```

#### Testing Schema Components (v4)
```php
livewire(EditPost::class)
    ->assertSchemaComponentExists('comments-section')
    ->assertSchemaComponentExists(
        'user-info',
        fn (Component $component): bool => $component->isVisible()
    );
```

#### Testing Actions in v4
```php
livewire(EditInvoice::class, ['record' => $invoice])
    ->callAction('send')
    ->assertNotified('Invoice sent successfully');

expect($invoice->refresh())->isSent()->toBeTrue();
```

#### Testing Multiple Panels (v4)
```php
use Filament\Facades\Filament;

// Set panel context for testing
Filament::setCurrentPanel('admin');

livewire(AdminDashboard::class)
    ->assertSuccessful();
```

### Testing Schema Components
```php
// Test schema component existence
livewire(EditPost::class)
    ->assertSchemaComponentExists('comments-section');

// Test with truth test
livewire(EditPost::class)
    ->assertSchemaComponentExists(
        'comments-section',
        fn (Component $component): bool => $component->getHeading() === 'Comments'
    );
```

---

## Resource Generation

### Artisan Commands
Always use proper Filament artisan commands:
```bash
php artisan make:filament-resource ModelName
php artisan make:filament-page PageName
php artisan make:filament-widget WidgetName
php artisan make:filament-relation-manager RelationName
php artisan make:filament-schema-component ComponentName
```

- Never manually create Filament files or directories
- Always pass `--no-interaction` to ensure commands work without user input

### Filament v4 Implementation Guidelines
- **Resource Classes**: Use Filament's schema components and table components for Eloquent models
- **Schema Architecture**: Leverage `Filament\Schemas\Components` for layout and prime components
- **Custom Components**: Create reusable schema components for complex UI patterns
- **Form Layout**: Use `Grid`, `Section`, `Fieldset` with `columnSpanFull()` for proper width control
- **Page Creation**: Extend `Filament\Pages\Page` for custom pages with full schema support
- **Modern Icons**: Use `Filament\Support\Icons\Heroicon` enum for consistent iconography
- **Action Logic**: All actions extend `Filament\Actions\Action` with unified behavior

### UI Guidelines
- **Filament v4 Conventions**: Follow v4 schema-driven architecture strictly
- **Schema Components**: Use prime components (`Text`, `Icon`, `Image`) for arbitrary content
- **Layout Components**: Apply `columnSpanFull()` when components should span full width
- **Tailwind CSS v4**: Ensure compatibility with `@source` directives in custom themes
- **Component Organization**: Group by feature using new v4 directory structure
- **Developer Experience**: Optimize for maintainability with schema classes and component extraction

---

## Module Integration

### FilamentPHP Integration with Modules
- **Module Resources**: Create Filament resources within modules
- **Panel Registration**: Every Module has a Filament plugin for registration inside app/Filament folder, which auto registered using Modules/Core/app/Filament/ModulesPluginsRegister, So you don't need to register it manually
- **Resource Organization**: Keep Filament resources organized by module
