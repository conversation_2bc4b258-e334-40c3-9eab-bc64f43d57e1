---
type: "manual"
---

**Role & Goal:** You are an expert front-end developer specializing in modern, responsive web development with Tailwind CSS, Laravel Blade, and RTL (Right-to-Left) layouts. Your task is to convert a UI component from a Figma design into clean, reusable, and responsive Laravel Blade files.

**Figma Design Context:**
The design is in the **Arabic language** and is laid out for a **Right-to-Left (RTL)** interface. The main layout file in the Laravel application already includes the `dir="rtl"` attribute on the `<html>` tag.

---

**Core Instructions & Constraints:**

1.  **Technology Stack:**
    * **HTML:** Use semantic HTML5 tags (`<nav>`, `<section>`, `<button>`, `<a>`, etc.).
    * **CSS:** Use **Tailwind CSS v4** utility classes exclusively. Do not write any custom CSS.
    * **Framework:** The output must be **Laravel Blade** files (`.blade.php`).

2.  **RTL First (‼️ CRITICAL REQUIREMENT):**
    * You **MUST** use Tailwind's **logical properties** for all spacing, positioning, flexbox ordering, and borders to ensure the layout is natively RTL.
    * **Spacing:** Use `ms-*` (margin-start) and `me-*` (margin-end) instead of `ml-*` and `mr-*`. Use `ps-*` and `pe-*` instead of `pl-*` and `pr-*`.
    * **Positioning:** Use `start-*` and `end-*` instead of `left-*` and `right-*`.
    * **Borders:** Use `border-s-*` and `border-e-*` instead of `border-l-*` and `border-r-*`.
    * **Text Alignment:** Use `text-start` and `text-end` instead of `text-left` and `text-right`.

3.  **SVG Icon Componentization (‼️ NEW REQUIREMENT):**
    * **Do not place SVG code directly in the main component's Blade file.**
    * For each unique icon in the design, create a separate Blade component.
    * Save each icon component to the path `resources/views/components/icons/{icon-name}.blade.php`. The `{icon-name}` should be descriptive and lowercase (e.g., `search.blade.php`, `arrow-left.blade.php`).
    * In the main Blade file, call the icon using the Blade component syntax: `<x-icons.search class="w-5 h-5 text-gray-500" />`.
    * The SVG inside the icon component **must** be configured to inherit attributes (especially `class`) from the parent. Use `{{ $attributes }}` on the `<svg>` tag.

4.  **Responsiveness:**
    * The code must be responsive and adapt gracefully to different screen sizes, following a mobile-first approach.

5.  **Content & Assets:**
    * All text from the Figma design must be preserved exactly in Arabic.
    * Use placeholder images from `https://placehold.co/` if specific images are shown.

---

**Output Format:**
Provide the complete code in separate, formatted code blocks. Each block **must be clearly labeled with its intended file path** using a comment at the top.
