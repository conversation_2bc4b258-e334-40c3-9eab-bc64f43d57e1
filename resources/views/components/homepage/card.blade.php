@props([
    'image' => '',
    'href' => '',
    'title' => '',
    'badge' => '',
    'badgeIcon' => '',
    'info' => '',
    'infoIcon' => '',
    'class' => ''
])

<a href="{{ $href ?? '#' }}" {{ $attributes->merge(['class' => 'bg-white dark:bg-zinc-800 rounded-2xl p-2 shadow-sm flex flex-col sm:flex-row gap-4 items-center h-full group transition-all duration-300 hover:shadow-lg hover:shadow-amber-100/20 dark:hover:shadow-amber-900/20 hover:-translate-y-1 hover:scale-[1.02] ' . $class]) }}>
    <!-- Course Content -->
    <div class="flex-1 flex flex-col gap-2 items-start p-2">

        <!-- Badge -->
        @if($badge)
        <div class="bg-amber-50 dark:bg-amber-900/20 px-3 py-1 rounded-lg flex items-center gap-1 group-hover:bg-amber-100 dark:group-hover:bg-amber-900/30 transition-colors duration-300">
            @if($badgeIcon)
                {!! $badgeIcon !!}
            @else
                <!-- Default levels icon -->
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.6701 1.74114C12.8243 1.83546 12.921 1.96792 13.0271 2.11166C13.0483 2.13956 13.0694 2.16746 13.0912 2.19621C13.1576 2.28409 13.2232 2.37246 13.2886 2.46102C13.3191 2.50211 13.3191 2.50211 13.3501 2.54404C13.4307 2.65307 13.5112 2.76222 13.5912 2.87164C13.7456 3.08245 13.9032 3.29066 14.0621 3.49813C14.2608 3.75781 14.4563 4.01957 14.6496 4.28338C14.8029 4.49223 14.9593 4.69863 15.1167 4.90438C15.3592 5.22127 15.5971 5.54119 15.8328 5.86316C15.8902 5.94153 15.9478 6.01974 16.0055 6.09791C16.0411 6.14611 16.0766 6.19431 16.1121 6.24252C16.1284 6.26451 16.1446 6.28651 16.1614 6.30917C16.3241 6.53049 16.3375 6.68715 16.3121 6.96102C16.231 7.13558 16.1278 7.22086 15.9605 7.31258C15.8557 7.33078 15.7537 7.32905 15.6477 7.32632C15.6183 7.32612 15.589 7.32592 15.5587 7.32571C15.4656 7.32493 15.3725 7.32317 15.2793 7.32137C15.2159 7.32067 15.1525 7.32003 15.0891 7.31945C14.9343 7.31791 14.7794 7.31548 14.6246 7.31258C14.6247 7.35163 14.6249 7.39067 14.6251 7.4309C14.629 8.38223 14.6318 9.33356 14.6337 10.2849C14.6346 10.745 14.6358 11.205 14.6378 11.6651C14.6396 12.0662 14.6407 12.4673 14.6411 12.8685C14.6413 13.0807 14.6419 13.293 14.6431 13.5053C14.6443 13.7053 14.6447 13.9054 14.6444 14.1055C14.6445 14.1787 14.6448 14.2518 14.6455 14.325C14.6509 14.9383 14.5738 15.4051 14.1289 15.8608C13.758 16.2127 13.3718 16.3233 12.8726 16.3222C12.8308 16.3224 12.7891 16.3226 12.746 16.3228C12.6305 16.3232 12.5149 16.3233 12.3994 16.3233C12.2746 16.3234 12.1498 16.3238 12.025 16.3242C11.7523 16.325 11.4796 16.3253 11.2069 16.3255C11.0365 16.3256 10.8661 16.3258 10.6956 16.3261C10.2233 16.3268 9.75088 16.3274 9.2785 16.3275C9.23318 16.3275 9.23318 16.3275 9.18695 16.3275C9.12572 16.3275 9.06449 16.3275 9.00325 16.3275C8.97286 16.3275 8.94246 16.3275 8.91114 16.3275C8.88071 16.3276 8.85028 16.3276 8.81893 16.3276C8.32617 16.3277 7.83343 16.3286 7.34068 16.33C6.83415 16.3313 6.32762 16.332 5.82109 16.332C5.53694 16.332 5.25281 16.3323 4.96867 16.3333C4.72664 16.3342 4.48462 16.3344 4.2426 16.3339C4.11928 16.3337 3.99598 16.3337 3.87266 16.3345C3.18348 16.3382 2.65472 16.3206 2.13887 15.817C1.85176 15.5144 1.68019 15.1587 1.67629 14.7371C1.67544 14.6677 1.67544 14.6677 1.67458 14.5969C1.67436 14.5469 1.67417 14.4969 1.67401 14.447C1.6737 14.3949 1.67338 14.3428 1.67305 14.2908C1.67247 14.1818 1.6722 14.0727 1.6721 13.9637C1.67191 13.8254 1.67059 13.6871 1.66898 13.5488C1.66792 13.4412 1.66768 13.3336 1.66764 13.226C1.6675 13.1751 1.66707 13.1242 1.66632 13.0733C1.6597 12.5801 1.758 12.1432 2.10893 11.7774C2.39426 11.4852 2.73418 11.2691 3.15136 11.2416C3.19703 11.2418 3.2427 11.2419 3.28975 11.2421C3.31516 11.2421 3.34057 11.2421 3.36675 11.2421C3.44988 11.2421 3.533 11.2427 3.61612 11.2432C3.6741 11.2433 3.73207 11.2434 3.79005 11.2435C3.94199 11.2438 4.09394 11.2445 4.24588 11.2453C4.40119 11.246 4.55649 11.2463 4.7118 11.2467C5.01606 11.2474 5.32031 11.2486 5.62456 11.2501C5.62418 11.2239 5.6238 11.1977 5.62341 11.1707C5.62189 11.0507 5.62095 10.9307 5.62003 10.8108C5.61942 10.7695 5.61881 10.7283 5.61819 10.6859C5.61522 10.1738 5.76154 9.81921 6.12018 9.4519C6.45326 9.13586 6.82393 8.98809 7.27889 8.9921C7.30978 8.99209 7.34068 8.99208 7.37251 8.99207C7.4739 8.99212 7.57528 8.99267 7.67667 8.99322C7.74726 8.99335 7.81784 8.99345 7.88843 8.99352C8.07367 8.99379 8.2589 8.99448 8.44413 8.99526C8.66668 8.9961 8.88923 8.99643 9.11177 8.99684C9.44937 8.99752 9.78696 8.99886 10.1246 9.00008C10.1246 8.44321 10.1246 7.88633 10.1246 7.31258C9.69439 7.31814 9.69439 7.31814 9.26424 7.32525C9.20998 7.32566 9.15572 7.32602 9.10146 7.32632C9.07369 7.32704 9.04593 7.32776 9.01732 7.3285C8.83002 7.32861 8.72706 7.2948 8.57768 7.17196C8.42598 7.01228 8.4189 6.86524 8.41152 6.65492C8.4763 6.36131 8.73123 6.10915 8.91167 5.87557C9.09581 5.63564 9.27798 5.39455 9.45659 5.15048C9.65298 4.88232 9.8536 4.61763 10.0556 4.35369C10.2097 4.15207 10.3614 3.94892 10.5113 3.74423C10.7074 3.47646 10.9077 3.21211 11.1095 2.9486C11.2928 2.70877 11.4726 2.46653 11.6502 2.22248C11.9145 1.86072 12.1832 1.52337 12.6701 1.74114Z" fill="#714F10"/>
                    <path d="M10.1246 8.99995C10.1246 11.4131 10.1246 13.8262 10.1246 16.3125C8.96295 16.3161 7.80135 16.3197 6.60454 16.3234C6.23827 16.325 5.872 16.3266 5.49463 16.3283C5.04607 16.3291 5.04607 16.3291 4.83583 16.3292C4.68954 16.3294 4.54326 16.3301 4.39697 16.331C4.20992 16.3322 4.02288 16.3326 3.83583 16.3323C3.76746 16.3324 3.6991 16.3327 3.63073 16.3334C3.03536 16.3391 2.57979 16.2473 2.13887 15.8168C1.85176 15.5142 1.68019 15.1586 1.67629 14.7369C1.67573 14.6907 1.67516 14.6444 1.67458 14.5968C1.67436 14.5468 1.67417 14.4968 1.67401 14.4468C1.6737 14.3948 1.67338 14.3427 1.67305 14.2906C1.67247 14.1816 1.6722 14.0726 1.6721 13.9636C1.67191 13.8253 1.67059 13.687 1.66898 13.5487C1.66792 13.4411 1.66768 13.3335 1.66764 13.2259C1.6675 13.175 1.66707 13.1241 1.66632 13.0731C1.6597 12.58 1.758 12.1431 2.10893 11.7773C2.39426 11.4851 2.73418 11.269 3.15136 11.2415C3.19703 11.2417 3.2427 11.2418 3.28975 11.242C3.31516 11.242 3.34057 11.242 3.36675 11.2419C3.44988 11.242 3.533 11.2425 3.61612 11.2431C3.6741 11.2432 3.73207 11.2433 3.79005 11.2434C3.94199 11.2437 4.09394 11.2443 4.24588 11.2451C4.40119 11.2458 4.55649 11.2462 4.7118 11.2465C5.01606 11.2473 5.32031 11.2485 5.62456 11.25C5.62418 11.2237 5.6238 11.1975 5.62341 11.1705C5.62189 11.0506 5.62095 10.9306 5.62003 10.8106C5.61942 10.7694 5.61881 10.7282 5.61819 10.6857C5.61522 10.1737 5.76154 9.81908 6.12018 9.45177C7.1408 8.48335 8.99904 8.99995 10.1246 8.99995Z" fill="#CA6C29"/>
                    <path d="M3.28975 11.242C3.32847 11.242 3.32847 11.242 3.36798 11.2419C3.4507 11.242 3.53341 11.2425 3.61612 11.2431C3.66753 11.2432 3.71894 11.2433 3.77035 11.2433C3.95995 11.2437 4.14955 11.2446 4.33916 11.2456C4.76334 11.247 5.18752 11.2485 5.62456 11.25C5.62456 12.9206 5.62456 14.5912 5.62456 16.3125C5.20545 16.3161 4.78635 16.3197 4.35454 16.3234C4.2232 16.325 4.09186 16.3266 3.95654 16.3283C3.79363 16.3291 3.79363 16.3291 3.71687 16.3292C3.66433 16.3295 3.61179 16.3301 3.55926 16.331C3.04792 16.3394 2.5992 16.2598 2.2144 15.8906C1.92479 15.6078 1.71139 15.272 1.67742 14.8595C1.67705 14.819 1.67667 14.7786 1.67629 14.7369C1.67544 14.6676 1.67544 14.6676 1.67458 14.5968C1.67436 14.5468 1.67417 14.4968 1.67401 14.4468C1.6737 14.3948 1.67338 14.3427 1.67305 14.2906C1.67247 14.1816 1.6722 14.0726 1.6721 13.9636C1.67191 13.8253 1.67059 13.687 1.66898 13.5487C1.66792 13.4411 1.66768 13.3335 1.66764 13.2259C1.6675 13.175 1.66707 13.124 1.66632 13.0731C1.6597 12.58 1.758 12.1431 2.10893 11.7773C2.43749 11.4408 2.81173 11.2403 3.28975 11.242Z" fill="#FAC638"/>
                </svg>
            @endif
            <span class="text-sm font-semibold text-amber-700 dark:text-amber-300 font-['IBM_Plex_Sans_Arabic']">{{ $badge }}</span>

        </div>
        @endif

        <!-- Course Title -->
        <h3 class="text-xl sm:text-2xl font-semibold text-gray-900 dark:text-white font-['IBM_Plex_Sans_Arabic'] leading-tight group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors duration-300">
            {{ $title }}
        </h3>

        <!-- Course Info -->
        @if($info)
        <div class="flex items-center gap-1">
            @if($infoIcon)
                {!! $infoIcon !!}
            @else
                <!-- Default book icon -->
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3.5298 0.804029C3.5894 0.803816 3.5894 0.803816 3.65019 0.8036C3.75969 0.80322 3.86918 0.803225 3.97867 0.803299C4.09708 0.803312 4.21548 0.802967 4.33388 0.802671C4.56557 0.802153 4.79727 0.80198 5.02896 0.801945C5.21744 0.801915 5.40592 0.801786 5.59439 0.801589C6.12956 0.801039 6.66472 0.80075 7.19988 0.800797C7.2431 0.800801 7.2431 0.800801 7.2872 0.800804C7.33047 0.800808 7.33047 0.800808 7.37462 0.800812C7.84188 0.800838 8.30913 0.80024 8.77638 0.799358C9.25693 0.798459 9.73748 0.798029 10.218 0.798083C10.4875 0.798104 10.757 0.797934 11.0265 0.797259C11.256 0.796688 11.4854 0.796556 11.7149 0.796979C11.8318 0.797182 11.9487 0.79719 12.0656 0.796644C12.7895 0.793476 13.4105 0.811293 13.9626 1.33729C13.9968 1.37137 13.9968 1.37137 14.0317 1.40614C14.0493 1.42305 14.067 1.43997 14.0852 1.45741C14.435 1.82389 14.5099 2.28756 14.5088 2.77155C14.5089 2.8184 14.5091 2.86525 14.5092 2.91209C14.5096 3.04044 14.5096 3.16878 14.5095 3.29712C14.5095 3.4357 14.5099 3.57427 14.5102 3.71285C14.5107 3.9842 14.5108 4.25556 14.5109 4.52692C14.5109 4.74757 14.511 4.96821 14.5112 5.18886C14.5118 5.81479 14.5121 6.44072 14.512 7.06665C14.512 7.10037 14.512 7.1341 14.512 7.16885C14.512 7.20262 14.512 7.23639 14.512 7.27119C14.512 7.81833 14.5126 8.36547 14.5135 8.91261C14.5144 9.47477 14.5148 10.0369 14.5147 10.5991C14.5147 10.9145 14.5149 11.23 14.5156 11.5455C14.5161 11.8141 14.5163 12.0827 14.5158 12.3513C14.5156 12.4883 14.5156 12.6252 14.5162 12.7622C14.5167 12.8878 14.5166 13.0134 14.516 13.1389C14.5159 13.1842 14.5161 13.2294 14.5164 13.2746C14.5203 13.8074 14.3687 14.2672 13.9907 14.6522C13.5559 15.0651 13.0575 15.1973 12.4711 15.1957C12.4115 15.196 12.4115 15.196 12.3507 15.1962C12.2412 15.1966 12.1317 15.1965 12.0222 15.1965C11.9038 15.1965 11.7854 15.1968 11.667 15.1971C11.4353 15.1976 11.2036 15.1978 10.9719 15.1978C10.7834 15.1979 10.595 15.198 10.4065 15.1982C9.87132 15.1987 9.33616 15.199 8.801 15.199C8.77218 15.199 8.74337 15.199 8.71368 15.199C8.68483 15.199 8.65598 15.199 8.62625 15.199C8.159 15.1989 7.69175 15.1995 7.2245 15.2004C6.74395 15.2013 6.2634 15.2017 5.78285 15.2017C5.51337 15.2017 5.24389 15.2018 4.97441 15.2025C4.74492 15.2031 4.51543 15.2032 4.28594 15.2028C4.16904 15.2026 4.05215 15.2026 3.93525 15.2031C3.21133 15.2063 2.5904 15.1885 2.03828 14.6625C2.01548 14.6398 1.99268 14.617 1.96919 14.5936C1.95155 14.5767 1.9339 14.5598 1.91572 14.5424C1.5659 14.1759 1.49094 13.7122 1.49208 13.2282C1.49197 13.1814 1.49182 13.1345 1.49165 13.0877C1.49127 12.9593 1.49128 12.831 1.49135 12.7026C1.49137 12.5641 1.49102 12.4255 1.49072 12.2869C1.49021 12.0156 1.49003 11.7442 1.49 11.4728C1.48997 11.2522 1.48984 11.0316 1.48964 10.8109C1.48909 10.185 1.4888 9.55906 1.48885 8.93313C1.48885 8.8994 1.48886 8.86567 1.48886 8.83092C1.48886 8.79715 1.48886 8.76338 1.48887 8.72858C1.48889 8.18144 1.48829 7.6343 1.48741 7.08716C1.48651 6.525 1.48608 5.96285 1.48614 5.40069C1.48616 5.08523 1.48599 4.76976 1.48531 4.4543C1.48474 4.18568 1.48461 3.91706 1.48503 3.64844C1.48524 3.51149 1.48524 3.37454 1.4847 3.23759C1.4842 3.112 1.4843 2.98642 1.48484 2.86083C1.48494 2.81561 1.48482 2.7704 1.48447 2.72519C1.48062 2.19233 1.63219 1.73259 2.0102 1.34754C2.44501 0.934712 2.94341 0.802482 3.5298 0.804029Z" fill="#714F10"/>
                    <path d="M6.81244 0.8125C8.47275 0.8125 10.1331 0.8125 11.8437 0.8125C11.8487 2.14782 11.8487 2.14782 11.8498 2.70789C11.8505 3.09442 11.8514 3.48095 11.8531 3.86748C11.8544 4.149 11.8552 4.43052 11.8555 4.71205C11.8556 4.86104 11.856 5.01004 11.8569 5.15903C11.8578 5.29944 11.858 5.43984 11.8578 5.58025C11.8579 5.63162 11.8581 5.68299 11.8586 5.73436C11.8615 6.06183 11.8527 6.38984 11.6142 6.64429C11.4057 6.8184 11.2094 6.89644 10.9381 6.89075C10.7259 6.85535 10.5536 6.76512 10.3787 6.64551C10.3595 6.63268 10.3402 6.61986 10.3204 6.60665C10.2597 6.56613 10.1991 6.52528 10.1386 6.48438C10.078 6.44349 10.0173 6.40269 9.95648 6.3621C9.91886 6.33699 9.88135 6.31172 9.84399 6.28624C9.63399 6.14587 9.44236 6.05551 9.18744 6.09375C8.89698 6.20387 8.64293 6.40112 8.38866 6.57604C8.33359 6.61383 8.27835 6.65134 8.22309 6.68884C8.19071 6.71109 8.15833 6.73333 8.12497 6.75625C7.91407 6.88275 7.7098 6.90388 7.46869 6.875C7.238 6.80465 7.06981 6.66896 6.93744 6.46875C6.89646 6.38542 6.87061 6.30848 6.84369 6.21875C6.83499 6.19085 6.83499 6.19086 6.8261 6.1624C6.80711 6.06698 6.80861 5.97551 6.80868 5.87822C6.80866 5.85655 6.80863 5.83488 6.8086 5.81255C6.80855 5.73995 6.80872 5.66735 6.80889 5.59476C6.8089 5.54271 6.8089 5.49066 6.80888 5.43861C6.80887 5.29728 6.80906 5.15596 6.80927 5.01464C6.80947 4.86695 6.80949 4.71927 6.80952 4.57158C6.80962 4.29191 6.80988 4.01224 6.81019 3.73257C6.81061 3.35466 6.81077 2.97674 6.81096 2.59883C6.81125 2.00339 6.81187 1.40794 6.81244 0.8125Z" fill="#FBC84F"/>
                </svg>
            @endif
            <span class="text-sm text-gray-600 dark:text-gray-400 font-['IBM_Plex_Sans_Arabic']">{{ $info }}</span>

        </div>
        @endif
    </div>
    <!-- Course Image -->
    <div class="w-full sm:w-[212px] h-[180px] bg-gradient-to-br from-amber-200 to-amber-300 dark:from-amber-600 dark:to-amber-700 rounded-2xl bg-cover bg-center flex-shrink-0 overflow-hidden group-hover:scale-105 transition-transform duration-300">
        @if($image)
            <img draggable="false" src="{{ $image }}" alt="{{ $title }}" class="w-full h-full object-cover rounded-2xl transition-transform duration-300 group-hover:scale-110">
        @else
            <!-- Default gradient background -->
            <div class="w-full h-full bg-gradient-to-br from-amber-200 to-amber-300 dark:from-amber-600 dark:to-amber-700 transition-all duration-300 group-hover:from-amber-300 group-hover:to-amber-400 dark:group-hover:from-amber-500 dark:group-hover:to-amber-600"></div>
        @endif
    </div>


</a>
