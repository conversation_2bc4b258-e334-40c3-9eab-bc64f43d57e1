<div {{ $attributes->class(['flex flex-col gap-3 items-end justify-start w-full']) }} x-data="{ activeLevel: 1 }">

    <!-- Level 1 - Beginner (Expanded by default) -->
    <div class="flex flex-col gap-1 items-end justify-start w-full">
        <!-- Level Header -->
        <div class="bg-white flex flex-row h-10 items-center justify-between px-2 py-0 rounded-[10px] w-full cursor-pointer"
             x-on:click="activeLevel = activeLevel === 1 ? 0 : 1">

            <!-- Level Title and Chevron (RIGHT SIDE) -->
            <div class="flex flex-row gap-3 items-center justify-end">
                <!-- Chevron Icon -->
                <div class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': activeLevel === 1 }">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 4.5L6 7.5L3 4.5" stroke="#6D6D6D" stroke-width="2" stroke-linecap="round"
                              stroke-linejoin="round"/>
                    </svg>
                </div>

                <div class="font-semibold text-[#181818] text-base tracking-[-0.32px]">
                    المستوى المبتدئ
                </div>
            </div>

            <!-- Level Badge (LEFT SIDE) -->
            <div :class="{ 'bg-[#f6ece0]  px-3' : activeLevel === 1 }"  class="flex flex-row gap-1 h-7 items-center justify-center py-2 rounded-[10px]">
                <!-- Level Icon -->
                <div class="w-[18px] h-[18px]">
                    <x-icons.levels level="1"/>
                </div>

                <div class="font-semibold text-[#e05e2f] text-sm">
                    المستــوى الأول
                </div>
            </div>            
        </div>

        <!-- Level Content -->
        <div class=" border border-[#e9e9e9] rounded-2xl w-full overflow-hidden transition-all duration-300 ease-in-out"
             x-show="activeLevel === 1"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95"
             style="box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.04), 0px 4px 4px 0px rgba(0,0,0,0.02);">

            <div class="p-4">
                <!-- Book Title Row -->
                <div class="flex flex-row gap-2 items-center justify-between rounded-[10px] w-full mb-2">

                    <!-- Book Icon (LEFT SIDE) -->
                    <div class="flex flex-row gap-2.5 items-center justify-start rounded-[999px]">
                        <div class="w-6 h-6">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.2968 5.85936C11.3769 5.93546 11.455 6.01373 11.5312 6.09374C11.6705 6.22026 11.8116 6.34459 11.953 6.46874C12.1172 6.39791 12.228 6.3011 12.3573 6.1787C13.3837 5.24471 14.7076 4.77428 16.0909 4.81493C17.2631 4.87279 18.5822 5.32711 19.3886 6.21678C19.5451 6.42358 19.6779 6.6299 19.6988 6.89281C19.7164 7.07645 19.7401 7.19581 19.828 7.35936C20.0603 7.54553 20.3429 7.66 20.613 7.78046C21.1227 8.01028 21.5008 8.32801 21.7175 8.85277C21.7911 9.08055 21.8029 9.29296 21.8037 9.53189C21.8039 9.56612 21.804 9.60034 21.8042 9.6356C21.8047 9.75006 21.8049 9.86451 21.8051 9.97896C21.8054 10.0611 21.8057 10.1433 21.806 10.2255C21.807 10.4952 21.8075 10.7649 21.808 11.0347C21.8082 11.1277 21.8083 11.2207 21.8085 11.3138C21.8094 11.7511 21.8101 12.1884 21.8105 12.6257C21.811 13.1296 21.8122 13.6334 21.8141 14.1372C21.8155 14.5272 21.8162 14.9172 21.8163 15.3072C21.8165 15.5399 21.8169 15.7725 21.8181 16.0051C21.8192 16.2243 21.8193 16.4434 21.8189 16.6626C21.8188 16.7426 21.8191 16.8226 21.8198 16.9026C21.8244 17.5095 21.7861 18.0842 21.3707 18.5658C21.0033 18.9369 20.5761 19.1446 20.0513 19.1731C19.6511 19.1692 19.2999 19.0442 18.9288 18.9089C18.5319 18.7659 18.13 18.6555 17.7187 18.5625C17.685 18.5547 17.6514 18.5469 17.6167 18.5388C15.9928 18.1803 14.2379 18.4182 12.6895 18.9607C12.658 18.9717 12.6265 18.9826 12.594 18.9938C12.5107 19.023 12.4278 19.0532 12.3449 19.0836C11.9306 19.1925 11.6113 19.0716 11.2192 18.9353C10.6127 18.7254 10.0124 18.5566 9.37492 18.4687C9.34465 18.4641 9.31438 18.4595 9.28319 18.4547C7.73148 18.2451 6.16072 18.4938 4.7045 19.0351C4.20938 19.2191 3.72675 19.2358 3.2343 19.0312C2.78107 18.8222 2.47044 18.5127 2.29307 18.0435C2.16738 17.6562 2.17672 17.2783 2.17879 16.8756C2.17853 16.7919 2.1782 16.7081 2.17779 16.6243C2.17694 16.3984 2.17726 16.1725 2.17786 15.9465C2.17834 15.7092 2.17789 15.4719 2.1776 15.2346C2.17726 14.8363 2.17771 14.438 2.1786 14.0397C2.17962 13.5801 2.17929 13.1206 2.17826 12.6611C2.17741 12.2654 2.17729 11.8697 2.17778 11.474C2.17807 11.2382 2.17811 11.0024 2.17749 10.7666C2.17695 10.5448 2.17733 10.3231 2.17841 10.1014C2.17866 10.0203 2.17859 9.93928 2.17817 9.85824C2.17511 9.19982 2.26285 8.6771 2.73233 8.18677C3.02975 7.90386 3.41871 7.76643 3.79323 7.61168C4.09552 7.48211 4.09552 7.48211 4.273 7.22249C4.29877 7.07528 4.29877 7.07528 4.30281 6.93261C4.32952 6.55541 4.59739 6.19512 4.87492 5.95311C6.73059 4.53975 9.41061 4.38856 11.2968 5.85936Z"
                                      fill="#E05E2F"/>
                                <path d="M19.2186 6.04676C19.5693 6.37038 19.764 6.80483 19.7893 7.2804C19.7921 7.47533 19.7918 7.67004 19.7904 7.86497C19.7902 7.93992 19.7901 8.01488 19.7899 8.08984C19.7895 8.29265 19.7885 8.49545 19.7873 8.69826C19.7863 8.86797 19.7856 9.03768 19.7849 9.20739C19.7832 9.60803 19.781 10.0087 19.7784 10.4093C19.7758 10.8216 19.7742 11.234 19.7732 11.6463C19.7722 12.0012 19.7705 12.3561 19.7683 12.7109C19.7669 12.9225 19.7659 13.1341 19.7656 13.3456C19.7652 13.5448 19.764 13.7439 19.7621 13.943C19.7616 14.0157 19.7613 14.0884 19.7614 14.1611C19.7621 14.7818 19.6681 15.2941 19.2186 15.7499C18.8027 16.1021 18.3392 16.2499 17.7982 16.2344C17.5553 16.2123 17.3223 16.1669 17.0845 16.1134C16.0479 15.8883 15.1113 16.1014 14.2262 16.6689C14.1367 16.7342 14.0525 16.8026 13.9686 16.8749C13.9387 16.8987 13.9087 16.9225 13.8779 16.947C13.6466 17.1373 13.4634 17.3535 13.2772 17.5874C13.1243 17.7777 12.9665 17.965 12.7967 18.1405C12.7812 18.1405 12.7658 18.1405 12.7498 18.1405C12.7353 16.5867 12.7227 15.0329 12.7128 13.4791C12.7116 13.2957 12.7104 13.1123 12.7092 12.9289C12.709 12.8924 12.7087 12.8558 12.7085 12.8182C12.7046 12.2269 12.6993 11.6355 12.6936 11.0442C12.6878 10.4375 12.6834 9.83078 12.6803 9.22405C12.6783 8.84961 12.6753 8.47522 12.6708 8.10081C12.6674 7.81377 12.6661 7.52676 12.6655 7.2397C12.665 7.1219 12.6638 7.00409 12.6619 6.8863C12.6594 6.72563 12.6592 6.56511 12.6596 6.40442C12.6584 6.35764 12.6571 6.31085 12.6558 6.26265C12.6599 5.9542 12.6599 5.9542 12.7828 5.81691C12.8782 5.74386 12.973 5.68326 13.0779 5.62489C13.1108 5.60515 13.1436 5.5854 13.1774 5.56506C15.134 4.39902 17.4584 4.65037 19.2186 6.04676Z"
                                      fill="#FFF1C3"/>
                                <path d="M10.7767 5.52309C10.8447 5.56109 10.9137 5.59732 10.9834 5.63205C11.2734 5.79384 11.2734 5.79384 11.3355 5.96554C11.3497 6.09796 11.3501 6.22456 11.3461 6.35769C11.3462 6.40789 11.3464 6.45809 11.3466 6.50981C11.3466 6.67808 11.3437 6.84616 11.3407 7.01441C11.3401 7.13498 11.3397 7.25556 11.3395 7.37614C11.3387 7.63575 11.3364 7.89531 11.333 8.15489C11.3282 8.53021 11.3256 8.90551 11.3236 9.28085C11.3204 9.88982 11.3152 10.4988 11.3089 11.1077C11.3028 11.6992 11.2975 12.2906 11.2934 12.8821C11.2931 12.9368 11.2931 12.9368 11.2927 12.9927C11.2914 13.1756 11.2902 13.3586 11.289 13.5415C11.2788 15.059 11.2655 16.5764 11.2502 18.0938C11.0689 17.9454 10.9194 17.809 10.7756 17.6221C10.0435 16.7139 9.20185 16.1385 8.01586 15.9844C7.51789 15.9481 6.83604 15.9415 6.37523 16.1719C5.77794 16.2119 5.33311 16.1864 4.86058 15.7852C4.44041 15.3815 4.26404 14.9542 4.24737 14.3708C4.24573 14.2339 4.24588 14.0971 4.24617 13.9602C4.24583 13.8844 4.24542 13.8086 4.24497 13.7327C4.24393 13.528 4.24379 13.3233 4.24388 13.1185C4.24388 12.9471 4.24348 12.7756 4.24309 12.6041C4.24219 12.1994 4.24205 11.7946 4.24237 11.3898C4.24268 10.9734 4.24161 10.557 4.23987 10.1406C4.23843 9.78203 4.2379 9.42342 4.23808 9.0648C4.23818 8.8511 4.23793 8.63742 4.23678 8.42373C4.23573 8.22265 4.23583 8.02159 4.23677 7.82051C4.23693 7.74712 4.2367 7.67373 4.23602 7.60035C4.23099 7.01193 4.32758 6.49711 4.75035 6.06009C6.43887 4.6142 8.85959 4.44348 10.7767 5.52309Z"
                                      fill="#FFF1C3"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Book Title (RIGHT SIDE) -->
                    <div class="flex flex-col gap-2 grow items-start justify-start">
                        <div class="font-bold text-[#63410a] text-base">
                            كتاب الطالب الأول (جزئين)
                        </div>
                    </div>
                </div>

                <!-- Content Points -->
                <div class="flex flex-col gap-2 items-start justify-start font-semibold text-sm text-neutral-600 w-full">
                    <div class="flex flex-col justify-center w-full">
                        <ul class="list-none space-y-2">
                            <li class="flex items-start gap-2 flex-row">
                                <div class="w-1.5 h-1.5 bg-neutral-600 rounded-full mt-2 flex-shrink-0"></div>
                                <span class="leading-[1.5]">
                                    يركز على الأساسيات، الحروف والمفردات المصوَّرة لضمان فهم سهل وممتاز.
                                </span>
                            </li>
                        </ul>
                    </div>

                    <div class="flex flex-col justify-center w-full">
                        <ul class="list-none space-y-2">
                            <li class="flex items-start gap-2 flex-row">
                                <div class="w-1.5 h-1.5 bg-neutral-600 rounded-full mt-2 flex-shrink-0"></div>
                                <span class="leading-[1.5]">
                                    يساعد الطالب على فهم واستخدام جمل بسيطة في الحياة اليومية.
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Level 2 - Intermediate -->
    <div class="bg-white flex flex-row h-10 items-center justify-between px-2 py-0 rounded-[10px] w-full cursor-pointer"
         x-on:click="activeLevel = activeLevel === 2 ? 0 : 2">

        <!-- Level Title and Chevron (RIGHT SIDE) -->
        <div class="flex flex-row gap-3 items-center justify-end">
            <!-- Chevron Icon -->
            <div class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': activeLevel === 2 }">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 4.5L6 7.5L3 4.5" stroke="#6D6D6D" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round"/>
                </svg>
            </div>

            <div class="font-medium text-base text-neutral-600 tracking-[-0.32px]">
                المستوى المتوسط
            </div>
        </div>

        <!-- Level Badge (LEFT SIDE) -->
        <div :class="{ 'bg-[#f6ece0]  px-3' : activeLevel === 2 }" class="flex flex-row gap-1 h-7 items-center justify-center px-0 py-2 rounded-[10px]">
            <!-- Level 2 Icon -->
            <div class="w-[18px] h-[18px]">
                <x-icons.levels level="2"/>
            </div>

            <div class="font-semibold text-[#e05e2f] text-sm">
                المستـوى الثاني
            </div>
        </div>

    </div>

    <!-- Level 2 Content -->
    <div class=" border border-[#e9e9e9] rounded-2xl w-full overflow-hidden transition-all duration-300 ease-in-out"
         x-show="activeLevel === 2"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         style="box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.04), 0px 4px 4px 0px rgba(0,0,0,0.02);">

        <div class="p-4">
            test
        </div>
    </div>

    <!-- Level 3 - Advanced -->
    <div class="bg-white flex flex-row h-10 items-center justify-between px-2 py-0 rounded-[10px] w-full cursor-pointer"
         x-on:click="activeLevel = activeLevel === 3 ? 0 : 3">

        <!-- Level Title and Chevron (RIGHT SIDE) -->
        <div class="flex flex-row gap-3 items-center justify-end">
            <!-- Chevron Icon -->
            <div class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': activeLevel === 3 }">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 4.5L6 7.5L3 4.5" stroke="#6D6D6D" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round"/>
                </svg>
            </div>

            <div class="font-medium text-base text-neutral-600 tracking-[-0.32px]">
                المستوى المتقدم
            </div>
        </div>

        <!-- Level Badge (LEFT SIDE) -->
        <div :class="{ 'bg-[#f6ece0]  px-3' : activeLevel === 3 }"  class="flex flex-row gap-1 h-7 items-center justify-center px-0 py-2 rounded-[10px]">
            <!-- Level 3 Icon -->
            <div class="w-[18px] h-[18px]">
                <x-icons.levels level="3"/>
            </div>

            <div class="font-semibold text-[#e05e2f] text-sm">
                المستوى الثالث
            </div>
        </div>
    </div>

    <!-- Level 3 Content -->
    <div class=" border border-[#e9e9e9] rounded-2xl w-full overflow-hidden transition-all duration-300 ease-in-out"
         x-show="activeLevel === 3"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         style="box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.04), 0px 4px 4px 0px rgba(0,0,0,0.02);">

        <div class="p-4">
            test
        </div>
    </div>

    <!-- Level 4 - Distinguished -->
    <div class="bg-white flex flex-row h-10 items-center justify-between px-2 py-0 rounded-[10px] w-full cursor-pointer"
         x-on:click="activeLevel = activeLevel === 4 ? 0 : 4">

        <!-- Level Title and Chevron (RIGHT SIDE) -->
        <div class="flex flex-row gap-3 items-center justify-end">
            <!-- Chevron Icon -->
            <div class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': activeLevel === 4 }">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 4.5L6 7.5L3 4.5" stroke="#6D6D6D" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round"/>
                </svg>
            </div>

            <div class="font-medium text-base text-neutral-600 tracking-[-0.32px]">
                المستوى المتميز
            </div>
        </div>

        <!-- Level Badge (LEFT SIDE) -->
        <div :class="{ 'bg-[#f6ece0]  px-3' : activeLevel === 4 }"  class="flex flex-row gap-1 h-7 items-center justify-center px-0 py-2 rounded-[10px]">
            <!-- Level 4 Icon -->
            <div class="w-[18px] h-[18px]">
                <x-icons.levels level="4"/>
            </div>

            <div class="font-semibold text-[#e05e2f] text-sm">
                المسـتـوى الرابع
            </div>
        </div>
    </div>

    <!-- Level 4 Content -->
    <div class=" border border-[#e9e9e9] rounded-2xl w-full overflow-hidden transition-all duration-300 ease-in-out"
         x-show="activeLevel === 4"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         style="box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.04), 0px 4px 4px 0px rgba(0,0,0,0.02);">

        <div class="p-4">
            test
        </div>
    </div>

</div>
