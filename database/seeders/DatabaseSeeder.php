<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Modules\Core\Models\Admin;
use Modules\Institute\Models\Institute;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        // User::factory(10)->create();

        // Admin::create([
        //     'name' => 'Admin',
        //     'email' => 'admin@'. config('app.domain'),
        //     'username' => 'admin',
        //     'password' => bcrypt('123123123'),
        // ]);

        $this->call([
            PermissionsSeeder::class,
            // PdfsSeeder::class,
            // PdfPagesSeeder::class,
            // MediaSeeder::class,
            // VideosSeeder::class,
            // AudioSeeder::class,
            // UsersSeeder::class,
            // CoursesSeeder::class,
            // ModulesSeeder::class,
            // UnitsSeeder::class,
            // LessonsSeeder::class,
            // MaterialsSeeder::class,
            // BlogCategoriesSeeder::class,
            // BlogPostsSeeder::class,
            // NavigationsSeeder::class,
        ]);

        // DB::transaction(function () {
        //     $member = \App\Models\User::create([
        //         'name' => 'User',
        //         'email' => 'user@'. config('app.domain'),
        //         'username' => 'user',
        //         'password' => bcrypt('123123123'),
        //     ]);

        //     $institute = Institute::create([
        //         'name' => 'Institute',
        //         'slug' => 'institute',
        //         'city_id' => 1,
        //         'address1' => 'address',
        //         'address2' => 'address',
        //         'phone' => '123123123',
        //         'web' => 'www.institute.com',
        //         'photo' => 'institute.png',
        //         'user_id' => $member->id,
        //         'email' => '<EMAIL>',
        //     ]);

        //     $member->institute()->attach($institute);
        // });


    }
}
