<?php

namespace Plugins\FrontendTranslatable\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;

class TranslationController extends Controller
{
    public function toggleTranslationMode(Request $request)
    {
        $currentMode = Session::get('translation_mode', false);
        Session::put('translation_mode', !$currentMode);

        return response()->json([
            'success' => true,
            'translation_mode' => !$currentMode
        ]);
    }

    public function saveTranslation(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required|string',
            'locale' => 'required|string|in:en,es,fr,de,ar' // Add more locales as needed
        ]);

        $locale = $request->get('locale');
        $key = $request->key;
        $value = $request->value;

        $filePath = lang_path("{$locale}.json");

        // Create the file if it doesn't exist
        if (!File::exists($filePath)) {
            File::put($filePath, '{}');
        }

        // Read existing translations
        $translations = json_decode(File::get($filePath), true) ?: [];

        // Update the translation
        $translations[$key] = $value;

        // Save back to file
        $result = File::put($filePath, json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        if ($result !== false) {
            // Clear Laravel's translation cache
            if (function_exists('app')) {
                app('translator')->getLoader()->load($locale, 'translations');
            }

            return response()->json([
                'success' => true,
                'message' => 'Translation saved successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to save translation'
        ], 500);
    }
}
