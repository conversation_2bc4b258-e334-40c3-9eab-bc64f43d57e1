<?php

namespace Plugins\FrontendTranslatable;

use Plugins\FrontendTranslatable\Console\Commands\TranslationManage;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class FrontendTranslatableServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     */
    public function boot()
    {
        // Register the @translatable Blade directive
        Blade::directive('translatable', function ($expression) {
            // Remove quotes from the expression if present
            $key = trim($expression, '"\'');

            return "<?php echo '<span class=\"translatable\" data-translation-key=\"' . {$expression} . '\">' . __({$expression}) . '</span>'; ?>";
        });

        Blade::directive('translatableText', function ($expression) {
            // if has second parameter, use it as a fallback
            $fallback = '';
            if (strpos($expression, ',') !== false) {
                list($key, $fallback) = explode(',', $expression, 2);
                $key = trim($key, '"\'');
                $fallback = trim($fallback, '"\'');
            } else {
                $key = trim($expression, '"\'');
            }
            return "<?php echo '<span class=\"translatable\" data-translation-key=\"' . {$key} . '\">' . __({$key}, ['fallback' => {$fallback}]) . '</span>'; ?>";
        });

            // register Console commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                TranslationManage::class,
            ]);
        }

        // register routes
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');

       // register views and components
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'frontend-translatable');
    }


    /**
     * Register the application services.
     */
    public function register()
    {

    }
}
