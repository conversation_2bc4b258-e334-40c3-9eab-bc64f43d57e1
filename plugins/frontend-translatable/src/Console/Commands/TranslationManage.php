<?php

namespace Plugins\FrontendTranslatable\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class TranslationManage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translation:manage
                            {action : The action to perform (export|import|sync)}
                            {--locale= : Specific locale to work with}
                            {--file= : File path for import/export}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage translation files (export, import, sync)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'export':
                $this->exportTranslations();
                break;
            case 'import':
                $this->importTranslations();
                break;
            case 'sync':
                $this->syncTranslations();
                break;
            default:
                $this->error("Invalid action. Use: export, import, or sync");
                return 1;
        }

        return 0;
    }

    private function exportTranslations()
    {
        $locale = $this->option('locale') ?? 'en';
        $filePath = $this->option('file') ?? storage_path("app/translations-{$locale}.json");

        $translationFile = lang_path("{$locale}.json");

        if (!File::exists($translationFile)) {
            $this->error("Translation file for locale '{$locale}' not found.");
            return;
        }

        $translations = json_decode(File::get($translationFile), true);

        if (File::put($filePath, json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            $this->info("Translations exported to: {$filePath}");
        } else {
            $this->error("Failed to export translations.");
        }
    }

    private function importTranslations()
    {
        $locale = $this->option('locale') ?? 'en';
        $filePath = $this->option('file');

        if (!$filePath) {
            $this->error("Please specify a file path with --file option");
            return;
        }

        if (!File::exists($filePath)) {
            $this->error("Import file not found: {$filePath}");
            return;
        }

        $translations = json_decode(File::get($filePath), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error("Invalid JSON in import file.");
            return;
        }

        $targetFile = lang_path("{$locale}.json");

        if (File::put($targetFile, json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            $this->info("Translations imported from: {$filePath} to {$locale}");
        } else {
            $this->error("Failed to import translations.");
        }
    }

    private function syncTranslations()
    {
        $baseLocale = 'en';
        $locales = ['es', 'fr', 'de', 'ar']; // Add more as needed

        $baseFile = lang_path("{$baseLocale}.json");

        if (!File::exists($baseFile)) {
            $this->error("Base translation file (en.json) not found.");
            return;
        }

        $baseTranslations = json_decode(File::get($baseFile), true);
        $baseKeys = array_keys($baseTranslations);

        foreach ($locales as $locale) {
            $localeFile = lang_path("{$locale}.json");

            if (File::exists($localeFile)) {
                $localeTranslations = json_decode(File::get($localeFile), true);
            } else {
                $localeTranslations = [];
                $this->info("Creating new translation file for: {$locale}");
            }

            $updated = false;

            // Add missing keys
            foreach ($baseKeys as $key) {
                if (!isset($localeTranslations[$key])) {
                    $localeTranslations[$key] = $baseTranslations[$key]; // Use English as placeholder
                    $updated = true;
                    $this->line("Added missing key '{$key}' to {$locale}");
                }
            }

            // Remove obsolete keys
            foreach ($localeTranslations as $key => $value) {
                if (!in_array($key, $baseKeys)) {
                    unset($localeTranslations[$key]);
                    $updated = true;
                    $this->line("Removed obsolete key '{$key}' from {$locale}");
                }
            }

            if ($updated) {
                File::put($localeFile, json_encode($localeTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                $this->info("Updated translation file: {$locale}.json");
            } else {
                $this->line("No changes needed for: {$locale}.json");
            }
        }

        $this->info("Translation sync completed!");
    }
}
