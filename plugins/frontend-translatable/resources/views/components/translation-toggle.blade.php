{{--
Translation Mode Component
Usage: <x-translation-toggle />
Place this component in your layout file to show the translation toggle button
--}}
@auth('admin')

@if(config('app.env') !== 'production')
<div id="translation-mode-controls">
    <style>
        .translation-mode-active .translatable {
            position: relative;
            border: 1px dashed #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            padding: 2px 4px;
            border-radius: 3px;
            margin: 1px;
        }

        .translation-mode-active .translatable:hover {
            background-color: rgba(59, 130, 246, 0.2);
            border-color: #1d4ed8;
        }

        .translatable-input {
            border: 1px solid #3b82f6;
            background-color: inherit;
            padding: 2px;
            font-family: inherit;
            font-size: inherit;
            color: #0D0D0D;
            line-height: inherit;
            min-width: 100px;
            outline: none;
            border-radius: 3px;
        }

        .translation-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 3px 7px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .translation-toggle:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .translation-toggle.active {
            background: #dc2626;
        }

        .translation-toggle.active:hover {
            background: #b91c1c;
        }

        .translation-success {
            position: fixed;
            bottom: 80px;
            right: 20px;
            z-index: 9999;
            background: #10b981;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .translation-success.show {
            opacity: 1;
        }
    </style>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Check if CSRF token exists
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            // Create translation toggle button
            const toggleButton = document.createElement("button");
            toggleButton.className = "translation-toggle";
            toggleButton.textContent = {!! json_encode(session('translation_mode') ? 'Exit Translation Mode' : 'Translation Mode') !!};

            @if(session('translation_mode'))
                toggleButton.classList.add("active");
                document.body.classList.add("translation-mode-active");
            @endif

            document.body.appendChild(toggleButton);

            // Create success message element
            const successMessage = document.createElement("div");
            successMessage.className = "translation-success";
            successMessage.textContent = "Translation saved!";
            document.body.appendChild(successMessage);

            function showSuccess() {
                successMessage.classList.add("show");
                setTimeout(() => {
                    successMessage.classList.remove("show");
                }, 2000);
            }

            // Toggle translation mode
            toggleButton.addEventListener("click", function(e) {

                if (!csrfToken) {
                    alert("CSRF token not found. Please refresh the page.");
                    return;
                }

                fetch("{{ url('/toggle-translation-mode') }}", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": csrfToken,
                        "Accept": "application/json",
                        "X-Requested-With": "XMLHttpRequest"
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert("Failed to toggle translation mode: " + (data.message || "Unknown error"));
                    }
                })
                .catch(error => {
                    console.error("Error:", error);
                    alert("An error occurred");
                });
            });




            // Make elements editable in translation mode
            @if(session('translation_mode'))
                document.querySelectorAll(".translatable").forEach(function(element) {
                    element.addEventListener("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        if (element.querySelector(".translatable-input")) {
                            return; // Already editing
                        }

                        const originalText = element.textContent.trim();
                        const translationKey = element.getAttribute("data-translation-key");

                        if (!translationKey) {
                            console.warn("No translation key found for element:", element);
                            return;
                        }

                        const input = document.createElement("input");
                        input.type = "text";
                        input.className = "translatable-input";
                        input.value = originalText;
                        input.setAttribute("data-original", originalText);
                        input.setAttribute("data-key", translationKey);

                        element.innerHTML = "";
                        element.appendChild(input);
                        input.focus();
                        input.select();

                        function saveTranslation() {
                            const newText = input.value.trim();

                            if (newText !== originalText && newText !== "") {
                                if (!csrfToken) {
                                    alert("CSRF token not found. Please refresh the page.");
                                    element.textContent = originalText;
                                    return;
                                }

                                fetch("{{ url('/save-translation') }}", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json",
                                        "X-CSRF-TOKEN": csrfToken,
                                        "Accept": "application/json"
                                    },
                                    body: JSON.stringify({
                                        key: translationKey,
                                        value: newText,
                                        locale: "{{ app()->getLocale() }}"
                                    })
                                })
                                .then(response => {
                                    console.log("Save response status:", response.status);

                                    if (!response.ok) {
                                        throw new Error(`HTTP error! status: ${response.status}`);
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    console.log("Save response data:", data);
                                    if (data.success) {
                                        element.textContent = newText;
                                        showSuccess();
                                    } else {
                                        element.textContent = originalText;
                                        alert("Failed to save translation: " + (data.message || "Unknown error"));
                                    }
                                })
                                .catch(error => {
                                    console.error("Save error:", error);
                                    element.textContent = originalText;
                                    alert("Failed to save translation: " + error.message);
                                });
                            } else {
                                element.textContent = originalText;
                            }
                        }

                        input.addEventListener("blur", saveTranslation);
                        input.addEventListener("keypress", function(e) {
                            if (e.key === "Enter") {
                                input.blur();
                            } else if (e.key === "Escape") {
                                element.textContent = originalText;
                            }
                        });
                    });
                });
            @endif
        });
    </script>
</div>
@endif
@endauth
