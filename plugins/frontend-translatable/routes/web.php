<?php
use Plugins\FrontendTranslatable\Http\Controllers\TranslationController;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'auth:admin'])->group(function () {
    Route::post('/toggle-translation-mode', [TranslationController::class, 'toggleTranslationMode'])->name('translation.toggle');
    Route::post('/save-translation', [TranslationController::class, 'saveTranslation'])->name('translation.save');
});

