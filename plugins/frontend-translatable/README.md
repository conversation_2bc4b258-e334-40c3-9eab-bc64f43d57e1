# Frontend Translatable Plugin

[![Latest Version on Packagist](https://img.shields.io/packagist/v/tenchology/frontend-translatable.svg?style=flat-square)](https://packagist.org/packages/tenchology/frontend-translatable)
[![Total Downloads](https://img.shields.io/packagist/dt/tenchology/frontend-translatable.svg?style=flat-square)](https://packagist.org/packages/tenchology/frontend-translatable)
![GitHub Actions](https://github.com/tenchology/frontend-translatable/actions/workflows/main.yml/badge.svg)

A Laravel plugin that provides inline translation editing capabilities for frontend content. Click on any text to edit translations directly on your website!

## Features

- ✅ **Inline Translation Editing**: Click on any text to edit translations directly
- ✅ **Real-time Saving**: Changes are saved instantly to JSON language files
- ✅ **Multi-language Support**: Support for multiple locales (en, es, fr, de, ar, etc.)
- ✅ **Visual Feedback**: Highlighted translatable elements in translation mode
- ✅ **Security**: Only works in non-production environments
- ✅ **Easy Integration**: Simple Blade directives and components

**Status**: 🟢 FULLY OPERATIONAL

## Installation

You can install the package via composer:

```bash
composer require tenchology/frontend-translatable
```

## Usage

```php
// Usage description here
```

### Testing

```bash
composer test
```

### Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information what has changed recently.

## Contributing

Please see [CONTRIBUTING](CONTRIBUTING.md) for details.

### Security

If you discover any security related issues, <NAME_EMAIL> instead of using the issue tracker.

## Credits

-   [Mahmoud](https://github.com/tenchology)
-   [All Contributors](../../contributors)

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.

## Laravel Package Boilerplate

This package was generated using the [Laravel Package Boilerplate](https://laravelpackageboilerplate.com).
